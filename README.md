# 学生拍照系统 (Student Photo System)

一个用于远程控制Android手机拍照的跨平台系统，包含PC端客户端和Android端应用。

## 系统概述

本系统由两部分组成：
- **PC端客户端**: 使用Python + PyQt5开发的桌面应用
- **Android端应用**: 使用Kotlin + CameraX开发的手机应用

通过WiFi网络连接，PC端可以远程控制Android手机进行拍照，照片会自动传输到PC端并进行方向校正。

## 主要功能

### 🔗 网络连接
- WiFi局域网通信
- 自动设备发现
- 稳定的TCP连接
- 断线自动重连

### 📸 拍照功能
- 远程一键拍照
- 自动方向检测和校正
- 高质量照片传输
- 实时状态反馈

### 🖼️ 照片管理
- 自动照片预览
- 本地照片保存
- 照片列表管理
- 批量操作支持

### 📱 设备状态
- 实时连接状态显示
- 设备方向监测
- 网络信息显示
- 错误状态提示

## 快速开始

### 1. Android端设置
1. 安装Android应用到手机
2. 授予相机和存储权限
3. 连接到WiFi网络
4. 记录显示的IP地址

### 2. PC端设置
1. 安装Python依赖：`pip install -r pc-client/requirements.txt`
2. 运行PC客户端：`python pc-client/main.py`
3. 输入手机IP地址并连接
4. 开始远程拍照

## 详细文档

- [PC端使用说明](pc-client/README.md)
- [Android端使用说明](README_ANDROID.md)

## 系统要求

### PC端
- Python 3.7+
- PyQt5
- Windows/macOS/Linux

### Android端
- Android 7.0+ (API 24+)
- 相机权限
- 网络权限

## 技术架构

```
┌─────────────────┐    WiFi/TCP    ┌─────────────────┐
│   PC客户端      │ ◄─────────────► │  Android应用    │
│                 │                │                 │
│ • Python/PyQt5  │                │ • Kotlin/CameraX│
│ • 照片接收显示  │                │ • 相机控制      │
│ • 远程控制界面  │                │ • 照片传输      │
│ • 文件管理      │                │ • 状态监控      │
└─────────────────┘                └─────────────────┘
```

## 开发环境

### PC端开发
```bash
cd pc-client
pip install -r requirements.txt
python main.py
```

### Android端开发
1. 使用Android Studio打开项目
2. 同步Gradle依赖
3. 连接设备或模拟器
4. 运行应用

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 更新日志

### v1.0.0 (2025-06-23)
- ✨ 初始版本发布
- 📸 基础拍照功能
- 🔗 WiFi网络通信
- 🔄 自动照片旋转校正
- 📱 跨平台支持

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交Issue
- 发送邮件
- 创建讨论