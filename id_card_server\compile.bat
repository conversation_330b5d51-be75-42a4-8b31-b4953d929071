@echo off
chcp 65001 >nul
echo 编译32位身份证桥接程序...
echo.

REM 设置Visual Studio 2022 Build Tools环境
call "C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Auxiliary\Build\vcvars32.bat"

echo.
echo 开始编译...
cl /EHsc /Fe:id_card_bridge.exe id_card_bridge.cpp ws2_32.lib

if errorlevel 1 (
    echo.
    echo 编译失败！
    pause
    exit /b 1
) else (
    echo.
    echo 编译成功！生成了 id_card_bridge.exe
    echo 这是32位程序，可以加载32位DLL
    echo.
    if exist id_card_bridge.exe (
        echo 文件信息:
        dir id_card_bridge.exe
    )
)

echo.
pause
