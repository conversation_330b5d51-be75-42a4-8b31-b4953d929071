#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
证件照设置管理模块
管理证件照的各种参数设置，包括尺寸、背景色、模型等
"""

import json
import os
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import logging

logger = logging.getLogger(__name__)

@dataclass
class PhotoSize:
    """证件照尺寸配置"""
    name: str
    width: int
    height: int
    description: str = ""

@dataclass
class BackgroundColor:
    """背景色配置"""
    name: str
    hex_value: str
    description: str = ""

@dataclass
class MattingModel:
    """抠图模型配置"""
    name: str
    model_id: str
    description: str = ""

@dataclass
class FaceDetectModel:
    """人脸检测模型配置"""
    name: str
    model_id: str
    description: str = ""

@dataclass
class PhotoSettings:
    """证件照设置"""
    # 基本设置
    size: PhotoSize
    background_color: BackgroundColor
    matting_model: MattingModel
    face_detect_model: FaceDetectModel
    
    # 高级设置
    dpi: int = 300
    hd: bool = True
    face_alignment: bool = True
    head_measure_ratio: float = 0.2
    head_height_ratio: float = 0.45
    top_distance_max: float = 0.12
    top_distance_min: float = 0.1
    
    # 图像调整
    brightness_strength: float = 0
    contrast_strength: float = 0
    sharpen_strength: float = 0
    saturation_strength: float = 0
    
    # 输出设置
    kb_size: Optional[int] = None
    render_mode: int = 0  # 0=纯色，1=上下渐变，2=中心渐变

class PhotoSettingsManager:
    """证件照设置管理器"""
    
    def __init__(self, config_file: str = "photo_settings.json"):
        self.config_file = config_file
        self.current_settings = self._load_default_settings()
        self._load_settings()
    
    def _get_default_sizes(self) -> List[PhotoSize]:
        """获取默认证件照尺寸"""
        return [
            PhotoSize("一寸", 295, 413, "25mm × 35mm"),
            PhotoSize("二寸", 413, 579, "35mm × 49mm"),
            PhotoSize("小二寸", 413, 531, "35mm × 45mm"),
            PhotoSize("大二寸", 413, 626, "35mm × 53mm"),
            PhotoSize("三寸", 649, 991, "55mm × 84mm"),
            PhotoSize("五寸", 1051, 1500, "89mm × 127mm"),
            PhotoSize("六寸", 1205, 1795, "102mm × 152mm"),
            PhotoSize("护照", 390, 567, "33mm × 48mm"),
            PhotoSize("港澳通行证", 390, 567, "33mm × 48mm"),
            PhotoSize("美国签证", 600, 600, "51mm × 51mm"),
            PhotoSize("日本签证", 354, 472, "30mm × 40mm"),
            PhotoSize("韩国签证", 413, 531, "35mm × 45mm"),
            PhotoSize("驾驶证", 260, 378, "22mm × 32mm"),
            PhotoSize("社保卡", 358, 441, "26mm × 32mm"),
            PhotoSize("教师资格证", 295, 413, "25mm × 35mm"),
        ]
    
    def _get_default_colors(self) -> List[BackgroundColor]:
        """获取默认背景色"""
        return [
            BackgroundColor("白色", "ffffff", "最常用的证件照背景色"),
            BackgroundColor("蓝色", "438edb", "常用于护照、签证等"),
            BackgroundColor("红色", "ff0000", "常用于毕业照、工作证等"),
            BackgroundColor("浅蓝色", "67c7f5", "较浅的蓝色背景"),
            BackgroundColor("深蓝色", "0052d9", "较深的蓝色背景"),
            BackgroundColor("灰色", "c0c0c0", "中性灰色背景"),
            BackgroundColor("浅灰色", "e6e6e6", "较浅的灰色背景"),
        ]
    
    def _get_default_matting_models(self) -> List[MattingModel]:
        """获取默认抠图模型"""
        return [
            MattingModel("ModNet专业人像", "modnet_photographic_portrait_matting", "专业人像抠图，效果最佳"),
            MattingModel("HiVision ModNet", "hivision_modnet", "HiVision优化版本"),
            MattingModel("RMBG-1.4", "rmbg-1.4", "通用背景移除模型"),
            MattingModel("BiRefNet-v1-lite", "birefnet-v1-lite", "轻量级高精度模型"),
        ]
    
    def _get_default_face_detect_models(self) -> List[FaceDetectModel]:
        """获取默认人脸检测模型"""
        return [
            FaceDetectModel("MTCNN", "mtcnn", "经典人脸检测模型，稳定可靠"),
            FaceDetectModel("Face++", "face_plusplus", "商业级人脸检测API"),
            FaceDetectModel("RetinaFace-ResNet50", "retinaface-resnet50", "高精度人脸检测模型"),
        ]
    
    def _load_default_settings(self) -> PhotoSettings:
        """加载默认设置"""
        sizes = self._get_default_sizes()
        colors = self._get_default_colors()
        matting_models = self._get_default_matting_models()
        face_detect_models = self._get_default_face_detect_models()
        
        return PhotoSettings(
            size=sizes[0],  # 一寸
            background_color=colors[0],  # 白色
            matting_model=matting_models[0],  # ModNet专业人像
            face_detect_model=face_detect_models[0],  # MTCNN
        )
    
    def _load_settings(self):
        """从文件加载设置"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 重建设置对象
                size_data = data.get('size', {})
                color_data = data.get('background_color', {})
                matting_data = data.get('matting_model', {})
                face_detect_data = data.get('face_detect_model', {})
                
                self.current_settings = PhotoSettings(
                    size=PhotoSize(**size_data) if size_data else self.current_settings.size,
                    background_color=BackgroundColor(**color_data) if color_data else self.current_settings.background_color,
                    matting_model=MattingModel(**matting_data) if matting_data else self.current_settings.matting_model,
                    face_detect_model=FaceDetectModel(**face_detect_data) if face_detect_data else self.current_settings.face_detect_model,
                    dpi=data.get('dpi', self.current_settings.dpi),
                    hd=data.get('hd', self.current_settings.hd),
                    face_alignment=data.get('face_alignment', self.current_settings.face_alignment),
                    head_measure_ratio=data.get('head_measure_ratio', self.current_settings.head_measure_ratio),
                    head_height_ratio=data.get('head_height_ratio', self.current_settings.head_height_ratio),
                    top_distance_max=data.get('top_distance_max', self.current_settings.top_distance_max),
                    top_distance_min=data.get('top_distance_min', self.current_settings.top_distance_min),
                    brightness_strength=data.get('brightness_strength', self.current_settings.brightness_strength),
                    contrast_strength=data.get('contrast_strength', self.current_settings.contrast_strength),
                    sharpen_strength=data.get('sharpen_strength', self.current_settings.sharpen_strength),
                    saturation_strength=data.get('saturation_strength', self.current_settings.saturation_strength),
                    kb_size=data.get('kb_size', self.current_settings.kb_size),
                    render_mode=data.get('render_mode', self.current_settings.render_mode),
                )
                
                logger.info("证件照设置已从文件加载")
            except Exception as e:
                logger.error(f"加载设置文件失败: {e}")
    
    def save_settings(self):
        """保存设置到文件"""
        try:
            data = asdict(self.current_settings)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logger.info("证件照设置已保存到文件")
        except Exception as e:
            logger.error(f"保存设置文件失败: {e}")
    
    def get_available_sizes(self) -> List[PhotoSize]:
        """获取可用的证件照尺寸"""
        return self._get_default_sizes()
    
    def get_available_colors(self) -> List[BackgroundColor]:
        """获取可用的背景色"""
        return self._get_default_colors()
    
    def get_available_matting_models(self) -> List[MattingModel]:
        """获取可用的抠图模型"""
        return self._get_default_matting_models()
    
    def get_available_face_detect_models(self) -> List[FaceDetectModel]:
        """获取可用的人脸检测模型"""
        return self._get_default_face_detect_models()
    
    def get_current_settings(self) -> PhotoSettings:
        """获取当前设置"""
        return self.current_settings
    
    def update_settings(self, **kwargs):
        """更新设置"""
        for key, value in kwargs.items():
            if hasattr(self.current_settings, key):
                setattr(self.current_settings, key, value)
        self.save_settings()
    
    def set_size_by_name(self, size_name: str):
        """根据名称设置尺寸"""
        sizes = self.get_available_sizes()
        for size in sizes:
            if size.name == size_name:
                self.current_settings.size = size
                self.save_settings()
                return
        raise ValueError(f"未找到尺寸: {size_name}")
    
    def set_color_by_name(self, color_name: str):
        """根据名称设置背景色"""
        colors = self.get_available_colors()
        for color in colors:
            if color.name == color_name:
                self.current_settings.background_color = color
                self.save_settings()
                return
        raise ValueError(f"未找到背景色: {color_name}")
    
    def set_matting_model_by_name(self, model_name: str):
        """根据名称设置抠图模型"""
        models = self.get_available_matting_models()
        for model in models:
            if model.name == model_name:
                self.current_settings.matting_model = model
                self.save_settings()
                return
        raise ValueError(f"未找到抠图模型: {model_name}")
    
    def set_face_detect_model_by_name(self, model_name: str):
        """根据名称设置人脸检测模型"""
        models = self.get_available_face_detect_models()
        for model in models:
            if model.name == model_name:
                self.current_settings.face_detect_model = model
                self.save_settings()
                return
        raise ValueError(f"未找到人脸检测模型: {model_name}")


# 全局设置管理器实例
_settings_manager = None

def get_photo_settings_manager() -> PhotoSettingsManager:
    """获取全局设置管理器实例"""
    global _settings_manager
    if _settings_manager is None:
        _settings_manager = PhotoSettingsManager()
    return _settings_manager
