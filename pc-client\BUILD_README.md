# StudentFoto PC客户端打包说明

## 概述

本文档说明如何将StudentFoto PC客户端打包为Windows可执行文件，以便在没有Python环境的设备上运行。

## 系统要求

### 开发环境要求
- Windows 7 或更高版本
- Python 3.7 或更高版本
- 已安装项目依赖包

### 目标运行环境
- Windows 7 或更高版本
- 无需Python环境
- 无需额外依赖

## 打包方法

### 方法一：简单打包（推荐）

1. **运行批处理文件**
   ```bash
   # 在pc-client目录下双击运行
   build.bat
   ```

2. **或者手动执行**
   ```bash
   # 安装依赖
   pip install -r requirements.txt
   
   # 运行打包脚本
   python build_exe.py
   ```

### 方法二：高级打包（包含图标和版本信息）

```bash
# 运行高级打包脚本
python advanced_build.py
```

## 打包输出

### 简单打包输出
- `release/StudentFoto-PC客户端.exe` - 可执行文件
- `release/使用说明.txt` - 使用说明

### 高级打包输出
- `release_advanced/StudentFoto-PC客户端.exe` - 可执行文件（含图标）
- `release_advanced/install.bat` - 安装脚本

## 使用打包后的程序

### 直接运行
1. 将`StudentFoto-PC客户端.exe`复制到目标计算机
2. 双击运行即可

### 使用安装脚本（高级打包）
1. 将整个`release_advanced`文件夹复制到目标计算机
2. 运行`install.bat`安装脚本
3. 程序将安装到用户目录，并创建桌面快捷方式

## 注意事项

### 首次运行
- 首次启动可能需要10-30秒时间
- Windows Defender可能会报警，选择"允许运行"
- 确保防火墙允许程序访问网络

### 文件大小
- 打包后的exe文件约50-100MB
- 包含了所有必要的Python运行时和依赖库

### 兼容性
- 支持Windows 7及以上版本
- 32位和64位系统均支持
- 无需管理员权限

## 故障排除

### 打包失败
1. **检查Python环境**
   ```bash
   python --version
   pip --version
   ```

2. **更新pip和setuptools**
   ```bash
   python -m pip install --upgrade pip setuptools
   ```

3. **清理缓存重新打包**
   ```bash
   # 删除build和dist目录
   rmdir /s build dist
   # 重新运行打包脚本
   ```

### 运行时错误
1. **缺少DLL文件**
   - 安装Microsoft Visual C++ Redistributable
   - 下载地址：https://aka.ms/vs/17/release/vc_redist.x64.exe

2. **网络连接问题**
   - 检查防火墙设置
   - 确保与Android设备在同一网络

3. **程序无响应**
   - 等待程序完全启动（首次较慢）
   - 检查系统资源使用情况

## 自定义打包

### 修改图标
1. 准备ICO格式图标文件
2. 修改`advanced_build.py`中的图标路径
3. 重新打包

### 修改程序信息
1. 编辑`advanced_build.py`中的版本信息
2. 修改公司名称、产品名称等
3. 重新打包

### 优化文件大小
1. 在spec文件中添加更多excludes
2. 使用UPX压缩（已默认启用）
3. 移除不必要的依赖

## 发布建议

### 创建发布包
1. 使用高级打包方式
2. 创建包含以下文件的压缩包：
   - `StudentFoto-PC客户端.exe`
   - `install.bat`
   - `使用说明.txt`
   - `系统要求.txt`

### 版本管理
1. 在文件名中包含版本号
2. 维护更新日志
3. 提供旧版本下载链接

## 技术细节

### 打包工具
- **PyInstaller**: 主要打包工具
- **UPX**: 可执行文件压缩
- **PIL**: 图标生成

### 包含的模块
- PyQt5 GUI框架
- Socket网络通信
- PIL图像处理
- 所有自定义模块

### 排除的模块
- tkinter
- matplotlib
- numpy
- scipy
- pandas

这些模块不是必需的，排除可以减小文件大小。
