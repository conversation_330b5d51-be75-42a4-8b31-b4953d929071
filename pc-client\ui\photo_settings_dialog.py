#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
证件照设置对话框
允许用户配置证件照的各种参数
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTabWidget,
                             QWidget, QLabel, QComboBox, QSpinBox, QDoubleSpinBox,
                             QCheckBox, QSlider, QPushButton, QGroupBox,
                             QGridLayout, QColorDialog, QLineEdit, QMessageBox,
                             QFormLayout, QFrame)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QColor, QPalette

from core.photo_settings import get_photo_settings_manager, PhotoSize, BackgroundColor

class PhotoSettingsDialog(QDialog):
    """证件照设置对话框"""
    
    settings_changed = pyqtSignal()  # 设置更改信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.settings_manager = get_photo_settings_manager()
        self.current_settings = self.settings_manager.get_current_settings()
        self.init_ui()
        self.load_current_settings()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("证件照设置")
        self.setModal(True)
        self.resize(500, 600)
        
        layout = QVBoxLayout(self)
        
        # 创建选项卡
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)

        # 基本设置选项卡
        basic_tab = self.create_basic_tab()
        tab_widget.addTab(basic_tab, "基本设置")

        # 高级设置选项卡
        advanced_tab = self.create_advanced_tab()
        tab_widget.addTab(advanced_tab, "高级设置")

        # 图像调整选项卡
        adjustment_tab = self.create_adjustment_tab()
        tab_widget.addTab(adjustment_tab, "图像调整")
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 重置按钮
        reset_btn = QPushButton("重置默认")
        reset_btn.clicked.connect(self.reset_to_default)
        button_layout.addWidget(reset_btn)
        
        button_layout.addStretch()
        
        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        # 确定按钮
        ok_btn = QPushButton("确定")
        ok_btn.clicked.connect(self.accept_settings)
        ok_btn.setDefault(True)
        button_layout.addWidget(ok_btn)
        
        layout.addLayout(button_layout)
        
    def create_basic_tab(self) -> QWidget:
        """创建基本设置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 证件照尺寸组
        size_group = QGroupBox("证件照尺寸")
        size_layout = QFormLayout(size_group)

        self.size_combo = QComboBox()
        sizes = self.settings_manager.get_available_sizes()
        for size in sizes:
            self.size_combo.addItem(f"{size.name} ({size.description})", size)
        # 添加自定义选项
        self.size_combo.addItem("自定义尺寸", "custom")
        size_layout.addRow("尺寸类型:", self.size_combo)

        # 尺寸信息显示
        self.size_info_label = QLabel()
        size_layout.addRow("尺寸信息:", self.size_info_label)

        # 自定义尺寸输入框（初始隐藏）
        self.custom_size_widget = QWidget()
        custom_size_layout = QHBoxLayout(self.custom_size_widget)
        custom_size_layout.setContentsMargins(0, 0, 0, 0)

        self.custom_width_spin = QSpinBox()
        self.custom_width_spin.setRange(100, 2000)
        self.custom_width_spin.setValue(295)
        self.custom_width_spin.setSuffix(" px")
        custom_size_layout.addWidget(QLabel("宽度:"))
        custom_size_layout.addWidget(self.custom_width_spin)

        custom_size_layout.addWidget(QLabel("×"))

        self.custom_height_spin = QSpinBox()
        self.custom_height_spin.setRange(100, 3000)
        self.custom_height_spin.setValue(413)
        self.custom_height_spin.setSuffix(" px")
        custom_size_layout.addWidget(QLabel("高度:"))
        custom_size_layout.addWidget(self.custom_height_spin)

        custom_size_layout.addStretch()

        self.custom_size_widget.setVisible(False)  # 初始隐藏
        size_layout.addRow("自定义尺寸:", self.custom_size_widget)

        layout.addWidget(size_group)
        
        # 背景色组
        color_group = QGroupBox("背景色设置")
        color_layout = QFormLayout(color_group)
        
        self.color_combo = QComboBox()
        colors = self.settings_manager.get_available_colors()
        for color in colors:
            self.color_combo.addItem(f"{color.name} ({color.description})", color)
        color_layout.addRow("背景色:", self.color_combo)
        
        # 自定义颜色
        custom_color_layout = QHBoxLayout()
        self.custom_color_edit = QLineEdit()
        self.custom_color_edit.setPlaceholderText("输入HEX颜色值，如: ffffff")
        custom_color_layout.addWidget(self.custom_color_edit)
        
        self.color_picker_btn = QPushButton("选择颜色")
        self.color_picker_btn.clicked.connect(self.pick_color)
        custom_color_layout.addWidget(self.color_picker_btn)
        
        color_layout.addRow("自定义颜色:", custom_color_layout)
        
        # 渲染模式
        self.render_combo = QComboBox()
        self.render_combo.addItems(["纯色", "上下渐变", "中心渐变"])
        color_layout.addRow("渲染模式:", self.render_combo)
        
        layout.addWidget(color_group)
        
        # 模型选择组
        model_group = QGroupBox("AI模型选择")
        model_layout = QFormLayout(model_group)
        
        # 抠图模型
        self.matting_combo = QComboBox()
        matting_models = self.settings_manager.get_available_matting_models()
        for model in matting_models:
            self.matting_combo.addItem(f"{model.name} - {model.description}", model)
        model_layout.addRow("抠图模型:", self.matting_combo)
        
        # 人脸检测模型
        self.face_detect_combo = QComboBox()
        face_models = self.settings_manager.get_available_face_detect_models()
        for model in face_models:
            self.face_detect_combo.addItem(f"{model.name} - {model.description}", model)
        model_layout.addRow("人脸检测:", self.face_detect_combo)
        
        layout.addWidget(model_group)
        
        layout.addStretch()
        
        # 连接信号
        self.size_combo.currentIndexChanged.connect(self.on_size_selection_changed)
        self.custom_width_spin.valueChanged.connect(self.update_custom_size_info)
        self.custom_height_spin.valueChanged.connect(self.update_custom_size_info)
        
        return widget
        
    def create_advanced_tab(self) -> QWidget:
        """创建高级设置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 输出设置组
        output_group = QGroupBox("输出设置")
        output_layout = QFormLayout(output_group)
        
        self.dpi_spin = QSpinBox()
        self.dpi_spin.setRange(72, 600)
        self.dpi_spin.setValue(300)
        self.dpi_spin.setSuffix(" DPI")
        output_layout.addRow("图像分辨率:", self.dpi_spin)
        
        self.hd_check = QCheckBox("生成高清证件照")
        self.hd_check.setChecked(True)
        output_layout.addRow("", self.hd_check)
        
        self.kb_spin = QSpinBox()
        self.kb_spin.setRange(0, 10000)
        self.kb_spin.setValue(0)
        self.kb_spin.setSuffix(" KB")
        self.kb_spin.setSpecialValueText("不限制")
        output_layout.addRow("文件大小限制:", self.kb_spin)
        
        layout.addWidget(output_group)
        
        # 人脸处理组
        face_group = QGroupBox("人脸处理设置")
        face_layout = QFormLayout(face_group)
        
        self.face_alignment_check = QCheckBox("启用人脸对齐")
        self.face_alignment_check.setChecked(True)
        face_layout.addRow("", self.face_alignment_check)
        
        self.head_measure_spin = QDoubleSpinBox()
        self.head_measure_spin.setRange(0.1, 0.5)
        self.head_measure_spin.setValue(0.2)
        self.head_measure_spin.setSingleStep(0.01)
        self.head_measure_spin.setDecimals(2)
        face_layout.addRow("面部面积比例:", self.head_measure_spin)
        
        self.head_height_spin = QDoubleSpinBox()
        self.head_height_spin.setRange(0.3, 0.7)
        self.head_height_spin.setValue(0.45)
        self.head_height_spin.setSingleStep(0.01)
        self.head_height_spin.setDecimals(2)
        face_layout.addRow("面部高度比例:", self.head_height_spin)
        
        self.top_distance_min_spin = QDoubleSpinBox()
        self.top_distance_min_spin.setRange(0.05, 0.2)
        self.top_distance_min_spin.setValue(0.1)
        self.top_distance_min_spin.setSingleStep(0.01)
        self.top_distance_min_spin.setDecimals(2)
        face_layout.addRow("顶部距离最小值:", self.top_distance_min_spin)
        
        self.top_distance_max_spin = QDoubleSpinBox()
        self.top_distance_max_spin.setRange(0.1, 0.3)
        self.top_distance_max_spin.setValue(0.12)
        self.top_distance_max_spin.setSingleStep(0.01)
        self.top_distance_max_spin.setDecimals(2)
        face_layout.addRow("顶部距离最大值:", self.top_distance_max_spin)
        
        layout.addWidget(face_group)
        
        layout.addStretch()
        
        return widget
        
    def create_adjustment_tab(self) -> QWidget:
        """创建图像调整选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 图像调整组
        adjustment_group = QGroupBox("图像调整")
        adjustment_layout = QFormLayout(adjustment_group)
        
        # 亮度调整
        brightness_layout = QHBoxLayout()
        self.brightness_slider = QSlider(Qt.Horizontal)
        self.brightness_slider.setRange(-100, 100)
        self.brightness_slider.setValue(0)
        self.brightness_label = QLabel("0")
        brightness_layout.addWidget(self.brightness_slider)
        brightness_layout.addWidget(self.brightness_label)
        adjustment_layout.addRow("亮度调整:", brightness_layout)
        
        # 对比度调整
        contrast_layout = QHBoxLayout()
        self.contrast_slider = QSlider(Qt.Horizontal)
        self.contrast_slider.setRange(-100, 100)
        self.contrast_slider.setValue(0)
        self.contrast_label = QLabel("0")
        contrast_layout.addWidget(self.contrast_slider)
        contrast_layout.addWidget(self.contrast_label)
        adjustment_layout.addRow("对比度调整:", contrast_layout)
        
        # 锐化调整
        sharpen_layout = QHBoxLayout()
        self.sharpen_slider = QSlider(Qt.Horizontal)
        self.sharpen_slider.setRange(0, 100)
        self.sharpen_slider.setValue(0)
        self.sharpen_label = QLabel("0")
        sharpen_layout.addWidget(self.sharpen_slider)
        sharpen_layout.addWidget(self.sharpen_label)
        adjustment_layout.addRow("锐化调整:", sharpen_layout)
        
        # 饱和度调整
        saturation_layout = QHBoxLayout()
        self.saturation_slider = QSlider(Qt.Horizontal)
        self.saturation_slider.setRange(-100, 100)
        self.saturation_slider.setValue(0)
        self.saturation_label = QLabel("0")
        saturation_layout.addWidget(self.saturation_slider)
        saturation_layout.addWidget(self.saturation_label)
        adjustment_layout.addRow("饱和度调整:", saturation_layout)
        
        layout.addWidget(adjustment_group)
        
        # 连接滑块信号
        self.brightness_slider.valueChanged.connect(lambda v: self.brightness_label.setText(str(v)))
        self.contrast_slider.valueChanged.connect(lambda v: self.contrast_label.setText(str(v)))
        self.sharpen_slider.valueChanged.connect(lambda v: self.sharpen_label.setText(str(v)))
        self.saturation_slider.valueChanged.connect(lambda v: self.saturation_label.setText(str(v)))
        
        layout.addStretch()

        return widget



    def load_current_settings(self):
        """加载当前设置到界面"""
        settings = self.current_settings

        # 基本设置
        sizes = self.settings_manager.get_available_sizes()
        size_found = False
        for i, size in enumerate(sizes):
            if size.name == settings.size.name:
                self.size_combo.setCurrentIndex(i)
                size_found = True
                break

        # 如果没有找到匹配的预设尺寸，说明是自定义尺寸
        if not size_found or settings.size.name == "自定义":
            # 选择自定义选项（最后一个）
            self.size_combo.setCurrentIndex(self.size_combo.count() - 1)
            # 设置自定义尺寸值
            self.custom_width_spin.setValue(settings.size.width)
            self.custom_height_spin.setValue(settings.size.height)

        colors = self.settings_manager.get_available_colors()
        for i, color in enumerate(colors):
            if color.name == settings.background_color.name:
                self.color_combo.setCurrentIndex(i)
                break

        matting_models = self.settings_manager.get_available_matting_models()
        for i, model in enumerate(matting_models):
            if model.name == settings.matting_model.name:
                self.matting_combo.setCurrentIndex(i)
                break

        face_models = self.settings_manager.get_available_face_detect_models()
        for i, model in enumerate(face_models):
            if model.name == settings.face_detect_model.name:
                self.face_detect_combo.setCurrentIndex(i)
                break

        self.render_combo.setCurrentIndex(settings.render_mode)

        # 高级设置
        self.dpi_spin.setValue(settings.dpi)
        self.hd_check.setChecked(settings.hd)
        self.kb_spin.setValue(settings.kb_size or 0)
        self.face_alignment_check.setChecked(settings.face_alignment)
        self.head_measure_spin.setValue(settings.head_measure_ratio)
        self.head_height_spin.setValue(settings.head_height_ratio)
        self.top_distance_min_spin.setValue(settings.top_distance_min)
        self.top_distance_max_spin.setValue(settings.top_distance_max)

        # 图像调整
        self.brightness_slider.setValue(int(settings.brightness_strength))
        self.contrast_slider.setValue(int(settings.contrast_strength))
        self.sharpen_slider.setValue(int(settings.sharpen_strength))
        self.saturation_slider.setValue(int(settings.saturation_strength))

        # 自定义设置
        self.custom_width_spin.setValue(settings.size.width)
        self.custom_height_spin.setValue(settings.size.height)

        # 更新界面
        self.on_size_selection_changed()

    def on_size_selection_changed(self):
        """尺寸选择变化处理"""
        current_data = self.size_combo.currentData()

        if current_data == "custom":
            # 选择了自定义尺寸
            self.custom_size_widget.setVisible(True)
            self.update_custom_size_info()
        else:
            # 选择了预设尺寸
            self.custom_size_widget.setVisible(False)
            if current_data:
                info_text = f"{current_data.width} × {current_data.height} 像素"
                self.size_info_label.setText(info_text)

    def update_custom_size_info(self):
        """更新自定义尺寸信息显示"""
        width = self.custom_width_spin.value()
        height = self.custom_height_spin.value()
        info_text = f"{width} × {height} 像素 (自定义)"
        self.size_info_label.setText(info_text)

    def pick_color(self):
        """打开颜色选择器"""
        color = QColorDialog.getColor(QColor("#ffffff"), self, "选择背景色")
        if color.isValid():
            hex_color = color.name()[1:]  # 去掉#号
            self.custom_color_edit.setText(hex_color)

    def reset_to_default(self):
        """重置为默认设置"""
        reply = QMessageBox.question(
            self, "确认重置",
            "确定要重置所有设置为默认值吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 重新加载默认设置
            self.settings_manager.current_settings = self.settings_manager._load_default_settings()
            self.current_settings = self.settings_manager.current_settings
            self.load_current_settings()

    def accept_settings(self):
        """接受设置并保存"""
        try:
            # 收集界面设置
            settings = self.current_settings

            # 基本设置
            current_size_data = self.size_combo.currentData()
            if current_size_data == "custom":
                # 使用自定义尺寸
                settings.size = PhotoSize(
                    "自定义",
                    self.custom_width_spin.value(),
                    self.custom_height_spin.value(),
                    f"{self.custom_width_spin.value()}×{self.custom_height_spin.value()}"
                )
            else:
                # 使用预设尺寸
                settings.size = current_size_data

            settings.background_color = self.color_combo.currentData()

            # 检查自定义颜色
            custom_color = self.custom_color_edit.text().strip()
            if custom_color:
                # 验证HEX颜色格式
                if len(custom_color) == 6 and all(c in '0123456789abcdefABCDEF' for c in custom_color):
                    settings.background_color = BackgroundColor("自定义", custom_color, "用户自定义颜色")
                else:
                    QMessageBox.warning(self, "颜色格式错误", "请输入正确的HEX颜色值（6位）")
                    return

            settings.matting_model = self.matting_combo.currentData()
            settings.face_detect_model = self.face_detect_combo.currentData()
            settings.render_mode = self.render_combo.currentIndex()

            # 高级设置
            settings.dpi = self.dpi_spin.value()
            settings.hd = self.hd_check.isChecked()
            settings.kb_size = self.kb_spin.value() if self.kb_spin.value() > 0 else None
            settings.face_alignment = self.face_alignment_check.isChecked()
            settings.head_measure_ratio = self.head_measure_spin.value()
            settings.head_height_ratio = self.head_height_spin.value()
            settings.top_distance_min = self.top_distance_min_spin.value()
            settings.top_distance_max = self.top_distance_max_spin.value()

            # 图像调整
            settings.brightness_strength = float(self.brightness_slider.value())
            settings.contrast_strength = float(self.contrast_slider.value())
            settings.sharpen_strength = float(self.sharpen_slider.value())
            settings.saturation_strength = float(self.saturation_slider.value())

            # 保存设置
            self.settings_manager.current_settings = settings
            self.settings_manager.save_settings()

            # 发出设置更改信号
            self.settings_changed.emit()

            # 关闭对话框
            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "保存设置失败", f"保存设置时发生错误：\n{str(e)}")

    def get_current_settings_summary(self) -> str:
        """获取当前设置摘要"""
        settings = self.current_settings
        return f"""
当前证件照设置：
• 尺寸：{settings.size.name} ({settings.size.width}×{settings.size.height})
• 背景色：{settings.background_color.name}
• 抠图模型：{settings.matting_model.name}
• 人脸检测：{settings.face_detect_model.name}
• 分辨率：{settings.dpi} DPI
• 高清模式：{'开启' if settings.hd else '关闭'}
        """.strip()
