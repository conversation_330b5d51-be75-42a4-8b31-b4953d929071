#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级打包脚本 - 包含图标、版本信息等
"""

import os
import sys
import subprocess
import shutil
import tempfile
from pathlib import Path

def create_icon():
    """创建应用图标"""
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # 创建64x64的图标
        size = 64
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # 绘制相机图标
        # 相机主体
        camera_rect = [8, 20, 56, 48]
        draw.rectangle(camera_rect, fill=(70, 130, 180), outline=(50, 100, 150), width=2)
        
        # 镜头
        lens_center = (32, 34)
        lens_radius = 10
        draw.ellipse([lens_center[0]-lens_radius, lens_center[1]-lens_radius,
                     lens_center[0]+lens_radius, lens_center[1]+lens_radius],
                    fill=(200, 200, 200), outline=(150, 150, 150), width=2)
        
        # 内镜头
        inner_radius = 6
        draw.ellipse([lens_center[0]-inner_radius, lens_center[1]-inner_radius,
                     lens_center[0]+inner_radius, lens_center[1]+inner_radius],
                    fill=(50, 50, 50))
        
        # 闪光灯
        draw.rectangle([45, 24, 50, 29], fill=(255, 255, 100))
        
        # 保存为ICO文件
        icon_path = "app_icon.ico"
        img.save(icon_path, format='ICO', sizes=[(64, 64), (32, 32), (16, 16)])
        print(f"✅ 创建图标文件: {icon_path}")
        return icon_path
        
    except ImportError:
        print("⚠️ PIL未安装，跳过图标创建")
        return None
    except Exception as e:
        print(f"⚠️ 图标创建失败: {e}")
        return None

def create_version_file():
    """创建版本信息文件"""
    version_content = '''# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1,0,0,0),
    prodvers=(1,0,0,0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'StudentFoto'),
        StringStruct(u'FileDescription', u'StudentFoto PC客户端'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'StudentFoto'),
        StringStruct(u'LegalCopyright', u'Copyright © 2025'),
        StringStruct(u'OriginalFilename', u'StudentFoto-PC客户端.exe'),
        StringStruct(u'ProductName', u'StudentFoto学生拍照系统'),
        StringStruct(u'ProductVersion', u'*******')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)'''
    
    with open('version_info.txt', 'w', encoding='utf-8') as f:
        f.write(version_content)
    print("✅ 创建版本信息文件: version_info.txt")
    return 'version_info.txt'

def create_advanced_spec(icon_path=None, version_file=None):
    """创建高级规格文件"""
    icon_line = f"icon='{icon_path}'," if icon_path else "icon=None,"
    version_line = f"version='{version_file}'," if version_file else ""
    
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config.py', '.'),
        ('core', 'core'),
        ('ui', 'ui'),
    ],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui', 
        'PyQt5.QtWidgets',
        'PyQt5.QtNetwork',
        'PIL.Image',
        'PIL.ImageDraw',
        'PIL.ImageFont',
        'socket',
        'threading',
        'time',
        'tempfile',
        'logging',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'scipy',
        'pandas',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='StudentFoto-PC客户端',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    {icon_line}
    {version_line}
)
'''
    
    with open('studentfoto_advanced.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    print("✅ 创建高级规格文件: studentfoto_advanced.spec")

def build_advanced():
    """执行高级打包"""
    print("开始高级打包...")
    
    # 创建图标
    icon_path = create_icon()
    
    # 创建版本信息
    version_file = create_version_file()
    
    # 创建规格文件
    create_advanced_spec(icon_path, version_file)
    
    # 安装PyInstaller
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
    except:
        pass
    
    # 构建
    try:
        subprocess.check_call([
            sys.executable, "-m", "PyInstaller", 
            "--clean",
            "--noconfirm",
            "studentfoto_advanced.spec"
        ])
        print("✅ 高级打包完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 高级打包失败: {e}")
        return False

def create_installer_script():
    """创建安装脚本"""
    installer_content = '''@echo off
chcp 65001 >nul
echo ================================================
echo StudentFoto PC客户端 安装程序
echo ================================================
echo.

set "INSTALL_DIR=%USERPROFILE%\\StudentFoto"

echo 正在创建安装目录...
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

echo 正在复制文件...
copy "StudentFoto-PC客户端.exe" "%INSTALL_DIR%\\" >nul
if errorlevel 1 (
    echo ❌ 文件复制失败
    pause
    exit /b 1
)

echo 正在创建桌面快捷方式...
set "SHORTCUT=%USERPROFILE%\\Desktop\\StudentFoto PC客户端.lnk"
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%SHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\\StudentFoto-PC客户端.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'StudentFoto学生拍照系统PC客户端'; $Shortcut.Save()"

echo.
echo ✅ 安装完成！
echo 📁 安装位置: %INSTALL_DIR%
echo 🖥️ 桌面快捷方式已创建
echo.
echo 按任意键退出...
pause >nul
'''
    
    with open('install.bat', 'w', encoding='utf-8') as f:
        f.write(installer_content)
    print("✅ 创建安装脚本: install.bat")

def main():
    """主函数"""
    print("=" * 50)
    print("StudentFoto PC客户端高级打包工具")
    print("=" * 50)
    
    if not Path("main.py").exists():
        print("❌ 请在pc-client目录下运行此脚本")
        return False
    
    # 执行高级打包
    if not build_advanced():
        return False
    
    # 创建发布包
    dist_dir = Path("dist")
    exe_file = dist_dir / "StudentFoto-PC客户端.exe"
    
    if exe_file.exists():
        # 创建发布目录
        release_dir = Path("release_advanced")
        if release_dir.exists():
            shutil.rmtree(release_dir)
        release_dir.mkdir()
        
        # 复制文件
        shutil.copy2(exe_file, release_dir)
        
        # 创建安装脚本
        old_cwd = os.getcwd()
        os.chdir(release_dir)
        create_installer_script()
        os.chdir(old_cwd)
        
        print(f"\n✅ 高级发布包创建完成: {release_dir.absolute()}")
        print("📦 包含: 可执行文件 + 安装脚本")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
