#define WIN32_LEAN_AND_MEAN
#include <windows.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <iostream>
#include <string>
#include <sstream>

#pragma comment(lib, "ws2_32.lib")

// 华视SDK函数声明
typedef int (*CVR_InitComm_t)(int Port);
typedef int (*CVR_Authenticate_t)();
typedef int (*CVR_AuthenticateForNoJudge_t)();
typedef int (*CVR_Read_FPContent_t)();
typedef int (*CVR_CloseComm_t)();
typedef int (*GetPeopleName_t)(char* strTmp, int* strLen);
typedef int (*GetPeopleSex_t)(char* strTmp, int* strLen);
typedef int (*GetPeopleBirthday_t)(char* strTmp, int* strLen);
typedef int (*GetPeopleIDCode_t)(char* strTmp, int* strLen);

class IDCardBridge {
private:
    HMODULE hDll;
    bool isConnected;
    
    // SDK函数指针
    CVR_InitComm_t CVR_InitComm;
    CVR_Authenticate_t CVR_Authenticate;
    CVR_AuthenticateForNoJudge_t CVR_AuthenticateForNoJudge;
    CVR_Read_FPContent_t CVR_Read_FPContent;
    CVR_CloseComm_t CVR_CloseComm;
    GetPeopleName_t GetPeopleName;
    GetPeopleSex_t GetPeopleSex;
    GetPeopleBirthday_t GetPeopleBirthday;
    GetPeopleIDCode_t GetPeopleIDCode;

public:
    IDCardBridge() : hDll(nullptr), isConnected(false) {
        loadDLL();
    }
    
    ~IDCardBridge() {
        disconnect();
        if (hDll) {
            FreeLibrary(hDll);
        }
    }
    
    bool loadDLL() {
        // 加载DLL
        hDll = LoadLibraryA("server\\Termb.dll");
        if (!hDll) {
            std::cerr << "Failed to load Termb.dll, error: " << GetLastError() << std::endl;
            return false;
        }
        
        // 获取函数地址
        CVR_InitComm = (CVR_InitComm_t)GetProcAddress(hDll, "CVR_InitComm");
        CVR_Authenticate = (CVR_Authenticate_t)GetProcAddress(hDll, "CVR_Authenticate");
        CVR_AuthenticateForNoJudge = (CVR_AuthenticateForNoJudge_t)GetProcAddress(hDll, "CVR_AuthenticateForNoJudge");
        CVR_Read_FPContent = (CVR_Read_FPContent_t)GetProcAddress(hDll, "CVR_Read_FPContent");
        CVR_CloseComm = (CVR_CloseComm_t)GetProcAddress(hDll, "CVR_CloseComm");
        GetPeopleName = (GetPeopleName_t)GetProcAddress(hDll, "GetPeopleName");
        GetPeopleSex = (GetPeopleSex_t)GetProcAddress(hDll, "GetPeopleSex");
        GetPeopleBirthday = (GetPeopleBirthday_t)GetProcAddress(hDll, "GetPeopleBirthday");
        GetPeopleIDCode = (GetPeopleIDCode_t)GetProcAddress(hDll, "GetPeopleIDCode");

        if (!CVR_InitComm || !CVR_Authenticate || !CVR_AuthenticateForNoJudge || !CVR_Read_FPContent ||
            !CVR_CloseComm || !GetPeopleName || !GetPeopleSex ||
            !GetPeopleBirthday || !GetPeopleIDCode) {
            std::cerr << "Failed to get function addresses" << std::endl;
            return false;
        }
        
        std::cout << "DLL loaded successfully" << std::endl;
        return true;
    }
    
    bool connect(int port = 1001) {
        if (!CVR_InitComm) return false;
        
        int result = CVR_InitComm(port);
        if (result == 1) {
            isConnected = true;
            std::cout << "Card reader connected on port " << port << std::endl;
            return true;
        }
        
        std::cerr << "Failed to connect card reader, result: " << result << std::endl;
        return false;
    }
    
    bool disconnect() {
        if (!isConnected || !CVR_CloseComm) return true;
        
        int result = CVR_CloseComm();
        isConnected = false;
        std::cout << "Card reader disconnected" << std::endl;
        return result == 1;
    }
    
    bool authenticateCard() {
        if (!isConnected || !CVR_Authenticate) return false;
        
        int result = CVR_Authenticate();
        return result == 1;
    }
    
    std::string getString(GetPeopleName_t func) {
        if (!func) return "";
        
        char buffer[256] = {0};
        int length = sizeof(buffer);
        
        int result = func(buffer, &length);
        if (result == 1) {
            // 转换GBK到UTF-8
            int wideLen = MultiByteToWideChar(CP_ACP, 0, buffer, -1, nullptr, 0);
            if (wideLen > 0) {
                wchar_t* wideStr = new wchar_t[wideLen];
                MultiByteToWideChar(CP_ACP, 0, buffer, -1, wideStr, wideLen);
                
                int utf8Len = WideCharToMultiByte(CP_UTF8, 0, wideStr, -1, nullptr, 0, nullptr, nullptr);
                if (utf8Len > 0) {
                    char* utf8Str = new char[utf8Len];
                    WideCharToMultiByte(CP_UTF8, 0, wideStr, -1, utf8Str, utf8Len, nullptr, nullptr);
                    
                    std::string result(utf8Str);
                    delete[] utf8Str;
                    delete[] wideStr;
                    return result;
                }
                delete[] wideStr;
            }
        }
        return "";
    }
    
    std::string readCardInfo() {
        if (!isConnected || !CVR_AuthenticateForNoJudge) {
            return "{\"success\": false, \"error\": \"Reader not connected or function not available\"}";
        }

        // Use continuous authentication mode
        int result = CVR_AuthenticateForNoJudge();
        if (result != 1) {
            std::string errorMsg = "{\"success\": false, \"error\": \"Card authentication failed: ";
            if (result == 2) {
                errorMsg += "No card found\"}";
            } else if (result == 3) {
                errorMsg += "Card selection failed\"}";
            } else if (result == 4) {
                errorMsg += "Reader not connected\"}";
            } else if (result == 0) {
                errorMsg += "DLL not loaded\"}";
            } else {
                errorMsg += "Unknown error\"}";
            }
            return errorMsg;
        }

        if (!CVR_Read_FPContent) {
            return "{\"success\": false, \"error\": \"Read function not available\"}";
        }

        int readResult = CVR_Read_FPContent();
        if (readResult != 1) {
            return "{\"success\": false, \"error\": \"Failed to read card content\"}";
        }

        std::string name = getString((GetPeopleName_t)GetPeopleName);
        std::string gender = getString((GetPeopleName_t)GetPeopleSex);
        std::string birthDate = getString((GetPeopleName_t)GetPeopleBirthday);
        std::string idNumber = getString((GetPeopleName_t)GetPeopleIDCode);

        // 验证必要字段
        if (name.empty() || gender.empty() || birthDate.empty() || idNumber.empty()) {
            return "{\"success\": false, \"error\": \"Incomplete card information\"}";
        }

        // 构建JSON响应
        std::ostringstream oss;
        oss << "{\"success\": true, \"data\": {";
        oss << "\"name\": \"" << name << "\", ";
        oss << "\"gender\": \"" << gender << "\", ";
        oss << "\"birth_date\": \"" << birthDate << "\", ";
        oss << "\"id_number\": \"" << idNumber << "\"";
        oss << "}}";

        return oss.str();
    }

    std::string checkCardPresent() {
        if (!isConnected) {
            return "{\"success\": false, \"error\": \"Not connected\"}";
        }

        if (!CVR_Authenticate) {
            return "{\"success\": false, \"error\": \"Authenticate function not available\"}";
        }

        // Try to authenticate card
        // Return values: 1=success, 2=no card, 3=select fail, 4=not connected, 0=dll not loaded
        int result = CVR_Authenticate();
        if (result == 1) {
            return "{\"success\": true, \"card_present\": true}";
        } else if (result == 2) {
            return "{\"success\": true, \"card_present\": false}";
        } else {
            std::ostringstream oss;
            oss << "{\"success\": false, \"error\": \"Authentication error: " << result << "\"}";
            return oss.str();
        }
    }
};

class SocketServer {
private:
    SOCKET serverSocket;
    IDCardBridge bridge;
    
public:
    SocketServer() : serverSocket(INVALID_SOCKET) {
        // 初始化Winsock
        WSADATA wsaData;
        int result = WSAStartup(MAKEWORD(2, 2), &wsaData);
        if (result != 0) {
            std::cerr << "WSAStartup failed: " << result << std::endl;
        }
    }
    
    ~SocketServer() {
        if (serverSocket != INVALID_SOCKET) {
            closesocket(serverSocket);
        }
        WSACleanup();
    }
    
    bool start(int port = 9091) {
        serverSocket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
        if (serverSocket == INVALID_SOCKET) {
            std::cerr << "Socket creation failed: " << WSAGetLastError() << std::endl;
            return false;
        }
        
        sockaddr_in serverAddr;
        serverAddr.sin_family = AF_INET;
        serverAddr.sin_addr.s_addr = inet_addr("127.0.0.1");
        serverAddr.sin_port = htons(port);
        
        if (bind(serverSocket, (sockaddr*)&serverAddr, sizeof(serverAddr)) == SOCKET_ERROR) {
            std::cerr << "Bind failed: " << WSAGetLastError() << std::endl;
            return false;
        }
        
        if (listen(serverSocket, 1) == SOCKET_ERROR) {
            std::cerr << "Listen failed: " << WSAGetLastError() << std::endl;
            return false;
        }
        
        std::cout << "Bridge server started on port " << port << std::endl;
        
        while (true) {
            sockaddr_in clientAddr;
            int clientAddrLen = sizeof(clientAddr);
            SOCKET clientSocket = accept(serverSocket, (sockaddr*)&clientAddr, &clientAddrLen);
            
            if (clientSocket == INVALID_SOCKET) {
                std::cerr << "Accept failed: " << WSAGetLastError() << std::endl;
                continue;
            }
            
            std::cout << "Client connected" << std::endl;
            handleClient(clientSocket);
            closesocket(clientSocket);
            std::cout << "Client disconnected" << std::endl;
        }
        
        return true;
    }
    
private:
    void handleClient(SOCKET clientSocket) {
        char buffer[4096];

        while (true) {
            int bytesReceived = recv(clientSocket, buffer, sizeof(buffer) - 1, 0);
            if (bytesReceived <= 0) break;

            buffer[bytesReceived] = '\0';
            std::string request(buffer);

            std::string response;

            if (request.find("\"command\": \"connect\"") != std::string::npos) {
                // 解析端口号（简单解析）
                int port = 1001;
                size_t portPos = request.find("\"port\":");
                if (portPos != std::string::npos) {
                    portPos += 7;
                    while (portPos < request.length() && (request[portPos] == ' ' || request[portPos] == ':')) portPos++;
                    if (portPos < request.length() && isdigit(request[portPos])) {
                        port = atoi(&request[portPos]);
                    }
                }

                bool success = bridge.connect(port);
                response = success ? "{\"success\": true}" : "{\"success\": false}";

            } else if (request.find("\"command\": \"disconnect\"") != std::string::npos) {
                bool success = bridge.disconnect();
                response = success ? "{\"success\": true}" : "{\"success\": false}";

            } else if (request.find("\"command\": \"read_card\"") != std::string::npos) {
                response = bridge.readCardInfo();

            } else if (request.find("\"command\": \"check_card\"") != std::string::npos) {
                response = bridge.checkCardPresent();

            } else {
                response = "{\"success\": false, \"error\": \"Unknown command\"}";
            }

            send(clientSocket, response.c_str(), response.length(), 0);
        }
    }
};

int main() {
    std::cout << "ID Card Bridge Server Starting..." << std::endl;
    
    SocketServer server;
    if (!server.start(9091)) {
        std::cerr << "Failed to start server" << std::endl;
        return 1;
    }
    
    return 0;
}
