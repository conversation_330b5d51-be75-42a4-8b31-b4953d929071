@echo off
chcp 65001 >nul
title StudentFoto 证件照系统 - 一键打包工具

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    StudentFoto 证件照系统                    ║
echo ║                        一键打包工具                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: Python未安装或未添加到系统PATH
    echo.
    echo 📋 解决方案:
    echo    1. 下载并安装Python 3.7或更高版本
    echo    2. 安装时勾选"Add Python to PATH"选项
    echo    3. 重新运行此脚本
    echo.
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

REM 检查项目目录
if not exist "pc-client" (
    echo ❌ 错误: 找不到pc-client目录
    echo 请确保在项目根目录下运行此脚本
    pause
    exit /b 1
)

if not exist "id_card_server" (
    echo ❌ 错误: 找不到id_card_server目录
    echo 请确保在项目根目录下运行此脚本
    pause
    exit /b 1
)

echo ✅ 项目目录检查通过

echo.
echo 🚀 开始自动打包...
echo.

REM 运行打包脚本
python "打包工具.py"

if errorlevel 1 (
    echo.
    echo ❌ 打包失败！
    echo.
    echo 🔧 常见问题解决方案:
    echo    1. 检查网络连接，确保能下载依赖包
    echo    2. 关闭杀毒软件的实时保护
    echo    3. 以管理员身份运行此脚本
    echo    4. 检查磁盘空间是否充足
    echo.
) else (
    echo.
    echo 🎉 打包成功完成！
    echo.
    echo 📁 安装包位置: packages\ 目录
    echo.
    echo 📋 下一步操作:
    echo    1. 将服务端安装包部署到服务器
    echo    2. 将PC端安装包部署到各工作站
    echo    3. 确保所有设备在同一网络环境
    echo.
)

echo 按任意键退出...
pause >nul
