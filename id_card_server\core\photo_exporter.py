#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
照片导出器
"""

import os
import csv
import logging
from pathlib import Path
from typing import Dict, List, Tuple
from PyQt5.QtCore import QObject, pyqtSignal, QThread

logger = logging.getLogger(__name__)

class PhotoExporter(QObject):
    """照片导出器"""
    
    # 信号定义
    export_progress = pyqtSignal(int, int)  # current, total
    export_finished = pyqtSignal(dict)      # result
    export_error = pyqtSignal(str)          # error message
    
    def __init__(self, database_manager):
        super().__init__()
        self.db = database_manager
    
    def export_photos_by_id_number(self, export_dir: str) -> Dict[str, int]:
        """按身份证号码导出证件照"""
        try:
            if not os.path.exists(export_dir):
                os.makedirs(export_dir)
                logger.info(f"创建导出目录: {export_dir}")
            
            # 获取所有已完成的任务
            completed_tasks = self.db.get_completed_tasks()
            total_tasks = len(completed_tasks)
            
            if total_tasks == 0:
                result = {"exported": 0, "failed": 0, "total": 0}
                self.export_finished.emit(result)
                return result
            
            exported_count = 0
            failed_count = 0
            
            for i, task in enumerate(completed_tasks):
                try:
                    task_id, name, gender, birth_date, id_number, photo_data, photo_filename, completed_time = task
                    
                    # 发送进度信号
                    self.export_progress.emit(i + 1, total_tasks)
                    
                    if photo_data:
                        # 获取文件扩展名
                        file_ext = Path(photo_filename).suffix if photo_filename else '.jpg'
                        
                        # 新文件名为身份证号码
                        new_filename = f"{id_number}{file_ext}"
                        new_path = os.path.join(export_dir, new_filename)
                        
                        # 保存照片数据到文件
                        with open(new_path, 'wb') as f:
                            f.write(photo_data)
                        
                        exported_count += 1
                        logger.info(f"导出: {name}({id_number}) -> {new_filename}")
                    else:
                        logger.warning(f"无照片数据: {name}({id_number})")
                        failed_count += 1
                        
                except Exception as e:
                    logger.error(f"导出失败 {name}({id_number}): {e}")
                    failed_count += 1
            
            result = {
                "exported": exported_count,
                "failed": failed_count,
                "total": total_tasks
            }
            
            logger.info(f"导出完成: 成功{exported_count}, 失败{failed_count}, 总计{total_tasks}")
            self.export_finished.emit(result)
            return result
            
        except Exception as e:
            logger.error(f"导出照片异常: {e}")
            self.export_error.emit(str(e))
            raise e
    
    def export_task_list(self, export_file: str) -> int:
        """导出任务列表到CSV文件"""
        try:
            completed_tasks = self.db.get_completed_tasks()
            
            with open(export_file, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                
                # 写入表头
                writer.writerow(['任务ID', '姓名', '性别', '出生日期', '身份证号码', '完成时间', '有照片'])
                
                # 写入数据
                for task in completed_tasks:
                    task_id, name, gender, birth_date, id_number, photo_data, photo_filename, completed_time = task
                    has_photo = "是" if photo_data else "否"
                    
                    writer.writerow([task_id, name, gender, birth_date, id_number, completed_time, has_photo])
            
            logger.info(f"任务列表导出完成: {export_file}, 记录数: {len(completed_tasks)}")
            return len(completed_tasks)
            
        except Exception as e:
            logger.error(f"导出任务列表异常: {e}")
            raise e
    
    def get_export_statistics(self) -> Dict[str, int]:
        """获取导出统计信息"""
        try:
            completed_tasks = self.db.get_completed_tasks()
            
            stats = {
                "total_completed": len(completed_tasks),
                "with_photo": 0,
                "without_photo": 0,
                "unique_persons": set()
            }
            
            for task in completed_tasks:
                task_id, name, gender, birth_date, id_number, photo_data, photo_filename, completed_time = task
                
                stats["unique_persons"].add(id_number)
                
                if photo_data:
                    stats["with_photo"] += 1
                else:
                    stats["without_photo"] += 1
            
            stats["unique_persons"] = len(stats["unique_persons"])
            
            return stats
            
        except Exception as e:
            logger.error(f"获取导出统计异常: {e}")
            raise e


class PhotoExportThread(QThread):
    """照片导出线程"""
    
    progress_updated = pyqtSignal(int, int)  # current, total
    export_finished = pyqtSignal(dict)       # result
    export_error = pyqtSignal(str)           # error message
    
    def __init__(self, exporter, export_dir):
        super().__init__()
        self.exporter = exporter
        self.export_dir = export_dir
    
    def run(self):
        """运行导出"""
        try:
            # 连接信号
            self.exporter.export_progress.connect(self.progress_updated)
            self.exporter.export_finished.connect(self.export_finished)
            self.exporter.export_error.connect(self.export_error)
            
            # 执行导出
            self.exporter.export_photos_by_id_number(self.export_dir)
            
        except Exception as e:
            self.export_error.emit(str(e))
