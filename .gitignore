*.iml
.gradle
/local.properties
/.idea/caches
/.idea/libraries
/.idea/modules.xml
/.idea/workspace.xml
/.idea/navEditor.xml
/.idea/assetWizardSettings.xml
.DS_Store
/build
/captures
.externalNativeBuild
.cxx
local.properties

# 忽略所有图片文件
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.tiff
*.webp

# 忽略测试和调试照片
**/debug_photo_*.jpg
**/analyze_photo_*.jpg
**/test_photo_*.jpg
**/captured_photo_*.jpg

# Python相关文件
*.pyc
*.pyo
*.pyd
__pycache__/
*/__pycache__/
*/*/__pycache__/
*/*/*/__pycache__/
*.so
*.egg
*.egg-info/
dist/
build/
.pytest_cache/
.coverage
.tox/
.venv/
venv/
env/
ENV/

# 忽略测试脚本
test_*.py
**/test_*.py

# 打包相关文件和目录
# PyInstaller生成的文件
pc-client/build/
pc-client/dist/
pc-client/*.spec
pc-client/*.ico
pc-client/version_info.txt

# 发布包和压缩文件
pc-client/release/
pc-client/release_advanced/
pc-client/StudentFoto-PC客户端-v*/
pc-client/*.zip
pc-client/*.exe

# 临时文件和缓存
pc-client/__pycache__/
pc-client/*/__pycache__/
pc-client/*/*/__pycache__/
*.tmp
*.temp

# C++编译相关文件
# 编译生成的可执行文件
*.exe
*.dll
*.lib
*.a
*.so
*.dylib

# 编译临时文件
*.obj
*.o
*.pdb
*.ilk
*.exp
*.idb
*.pch
*.res
*.rc
*.manifest

# Visual Studio相关
*.vcxproj.user
*.vcxproj.filters
*.sdf
*.opensdf
*.suo
*.user
*.aps
*.pch
*.vspscc
*.vssscc
.builds
*.pidb
*.log
*.scc

# 身份证读卡器服务端编译文件
id_card_server/*.exe
id_card_server/*.obj
id_card_server/*.pdb
id_card_server/*.ilk
id_card_server/*.exp
id_card_server/*.lib

# 数据库文件
id_card_server/*.db
id_card_server/*.sqlite
id_card_server/*.sqlite3

# 日志文件
id_card_server/*.log
*.log
