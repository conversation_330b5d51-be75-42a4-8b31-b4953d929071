#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试任务分配流程
"""

import socket
import json
import time
import sys
import os
sys.path.append('.')

from id_card_server.core.database import DatabaseManager

def debug_task_assignment():
    """调试任务分配流程"""
    try:
        print("=== 调试任务分配流程 ===")
        
        # 1. 检查数据库状态
        print("1. 检查数据库状态...")
        db = DatabaseManager()
        
        # 检查正式任务表
        formal_stats = db.get_task_statistics("photo_tasks")
        print(f"   正式任务 - 总数: {formal_stats['total']}, 待分配: {formal_stats['pending']}")
        
        # 检查测试任务表
        test_stats = db.get_task_statistics("test_tasks")
        print(f"   测试任务 - 总数: {test_stats['total']}, 待分配: {test_stats['pending']}")
        
        # 显示测试任务详情
        if test_stats['pending'] > 0:
            print("   测试任务详情:")
            test_tasks = db.get_unassigned_tasks("test_tasks")
            for i, task in enumerate(test_tasks[:3]):
                print(f"     {i+1}. ID: {task['task_id']}, 姓名: {task['name']}, 状态: {task['task_status']}")
        
        # 2. 测试连接和ready_for_tasks流程
        print("\n2. 测试连接流程...")
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(30.0)
        
        try:
            sock.connect(("10.21.7.254", 9090))
            print("   ✅ Socket连接成功")
            
            # 发送连接消息
            connect_msg = {
                "type": "connect",
                "client_info": {
                    "client_type": "pc_client",
                    "version": "1.0.0",
                    "machine_id": "DEBUG_CLIENT_123",
                    "machine_info": {"debug": True}
                }
            }
            
            sock.send(json.dumps(connect_msg).encode('utf-8'))
            
            # 接收连接响应
            response_data = sock.recv(8192).decode('utf-8')
            if response_data:
                response = json.loads(response_data)
                print(f"   ✅ 连接响应: 状态={response.get('status')}, 机位={response.get('station_number')}")
            else:
                print("   ❌ 连接响应为空")
                return False
            
            # 发送ready_for_tasks消息
            print("   发送ready_for_tasks消息...")
            ready_msg = {
                "type": "ready_for_tasks",
                "client_id": "DEBUG_CLIENT_123",
                "timestamp": time.time()
            }
            
            sock.send(json.dumps(ready_msg).encode('utf-8'))
            
            # 接收ready_for_tasks响应
            response_data = sock.recv(8192).decode('utf-8')
            if response_data:
                response = json.loads(response_data)
                print(f"   ✅ ready_for_tasks响应: 状态={response.get('status')}, 消息={response.get('message')}")
                print(f"   任务发送数量: {response.get('tasks_sent', 0)}")
            else:
                print("   ❌ ready_for_tasks响应为空")
                return False
            
            # 3. 等待任务分配消息
            print("\n3. 等待任务分配消息...")
            sock.settimeout(15.0)
            
            messages_received = 0
            start_time = time.time()
            
            while time.time() - start_time < 15:
                try:
                    data = sock.recv(8192).decode('utf-8')
                    if data:
                        messages_received += 1
                        try:
                            msg = json.loads(data)
                            print(f"   收到消息 #{messages_received}: 类型={msg.get('type')}")
                            
                            if msg.get('type') == 'task_assigned':
                                task = msg.get('task', {})
                                print(f"   🎉 收到任务分配!")
                                print(f"     任务ID: {task.get('task_id')}")
                                print(f"     姓名: {task.get('name')}")
                                print(f"     身份证: {task.get('id_number')}")
                                return True
                            else:
                                print(f"     消息内容: {msg}")
                        except json.JSONDecodeError:
                            print(f"   收到非JSON消息: {data[:100]}...")
                    else:
                        print("   收到空消息")
                        break
                except socket.timeout:
                    print("   等待消息超时")
                    break
                except Exception as e:
                    print(f"   接收消息异常: {e}")
                    break
            
            print(f"   总共收到 {messages_received} 条消息")
            
            # 4. 主动请求任务
            print("\n4. 主动请求任务...")
            get_task_msg = {"type": "get_task"}
            sock.send(json.dumps(get_task_msg).encode('utf-8'))
            
            sock.settimeout(10.0)
            try:
                response_data = sock.recv(8192).decode('utf-8')
                if response_data:
                    response = json.loads(response_data)
                    print(f"   get_task响应: 状态={response.get('status')}")
                    
                    if response.get('status') == 'success' and 'task' in response:
                        task = response['task']
                        print(f"   🎉 主动获取任务成功!")
                        print(f"     任务ID: {task.get('task_id')}")
                        print(f"     姓名: {task.get('name')}")
                        return True
                    elif response.get('status') == 'no_task':
                        print("   ℹ️ 服务器报告没有可分配的任务")
                    else:
                        print(f"   ❌ 获取任务失败: {response.get('message')}")
                else:
                    print("   ❌ get_task响应为空")
            except socket.timeout:
                print("   ❌ get_task响应超时")
            
        except Exception as e:
            print(f"   ❌ 连接异常: {e}")
            return False
        finally:
            sock.close()
            print("   Socket已关闭")
        
        # 5. 再次检查数据库状态
        print("\n5. 检查测试后的数据库状态...")
        test_stats_after = db.get_task_statistics("test_tasks")
        print(f"   测试任务 - 总数: {test_stats_after['total']}, 待分配: {test_stats_after['pending']}, 已分配: {test_stats_after['assigned']}")
        
        if test_stats_after['assigned'] > test_stats['assigned']:
            print("   ✅ 有任务被分配了!")
        else:
            print("   ❌ 没有任务被分配")
        
        return False
        
    except Exception as e:
        print(f"❌ 调试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_task_assignment()
    if success:
        print("\n🎉 任务分配调试成功！")
    else:
        print("\n❌ 任务分配调试失败！")
        print("\n可能的原因:")
        print("1. 服务器的任务来源仍然设置为正式任务表")
        print("2. 服务器的check_and_assign_tasks方法没有被触发")
        print("3. 客户端的ready_for_tasks状态没有正确设置")
        print("4. 任务分配的条件检查失败")
