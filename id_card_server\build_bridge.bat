@echo off
echo 编译32位身份证桥接程序...

REM 检查是否有Visual Studio编译器
where cl >nul 2>&1
if errorlevel 1 (
    echo 未找到Visual Studio编译器，尝试设置环境...
    
    REM 尝试找到Visual Studio
    if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars32.bat" (
        call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars32.bat"
    ) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2017\Community\VC\Auxiliary\Build\vcvars32.bat" (
        call "C:\Program Files (x86)\Microsoft Visual Studio\2017\Community\VC\Auxiliary\Build\vcvars32.bat"
    ) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars32.bat" (
        call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars32.bat"
    ) else (
        echo 错误: 未找到Visual Studio，请安装Visual Studio或使用Developer Command Prompt
        pause
        exit /b 1
    )
)

REM 编译32位程序
echo 正在编译...
cl /EHsc /Fe:id_card_bridge.exe id_card_bridge.cpp ws2_32.lib

if errorlevel 1 (
    echo 编译失败
    pause
    exit /b 1
) else (
    echo 编译成功！生成了 id_card_bridge.exe
    echo.
    echo 注意：这是32位程序，可以加载32位DLL
)

pause
