#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
身份证读卡器服务端主程序
"""

import sys
import os
import logging
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ui.main_window import MainWindow
import config

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=getattr(logging, config.LOG_LEVEL),
        format=config.LOG_FORMAT,
        handlers=[
            logging.FileHandler('id_card_server.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def check_dependencies():
    """检查依赖"""
    try:
        # 检查SDK文件
        sdk_files = [
            "server/Termb.dll",
            "server/WltRS.dll",
            "server/sdtapi.dll",
            "server/license.dat"
        ]

        missing_files = []
        for file_path in sdk_files:
            if not os.path.exists(file_path):
                missing_files.append(file_path)

        if missing_files:
            return False, f"缺少SDK文件:\n" + "\n".join(missing_files)

        # 注意：不在这里测试DLL加载，让程序启动后再处理
        return True, "依赖检查通过"

    except Exception as e:
        return False, f"依赖检查失败: {e}"

def main():
    """主函数"""
    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info(f"启动 {config.APP_NAME} v{config.APP_VERSION}")
    
    # 检查依赖
    deps_ok, deps_msg = check_dependencies()
    if not deps_ok:
        print(f"错误: {deps_msg}")
        return 1
    
    # 设置应用属性（必须在创建QApplication之前）
    try:
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    except AttributeError:
        # 旧版本Qt可能没有这些属性
        pass

    # 创建应用
    app = QApplication(sys.argv)
    app.setApplicationName(config.APP_NAME)
    app.setApplicationVersion(config.APP_VERSION)
    
    try:
        # 创建主窗口
        window = MainWindow()
        window.show()
        
        logger.info("应用程序启动成功")
        
        # 运行应用
        return app.exec_()
        
    except Exception as e:
        logger.error(f"应用程序启动失败: {e}")
        QMessageBox.critical(
            None, "启动失败", 
            f"应用程序启动失败：\n{str(e)}"
        )
        return 1

if __name__ == "__main__":
    sys.exit(main())
