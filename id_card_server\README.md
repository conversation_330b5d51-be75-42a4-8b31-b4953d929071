# 身份证读卡器服务端

基于华视电子身份证阅读器SDK的服务端程序，支持身份证信息读取、任务管理和照片导出功能。

## 功能特点

✅ **身份证读取**: 支持华视电子身份证阅读器，读取身份证基本信息  
✅ **任务管理**: 自动创建拍摄任务，支持多PC端连接和任务分配  
✅ **照片管理**: 接收PC端上传的证件照，存储在数据库中  
✅ **照片导出**: 按身份证号码导出照片文件  
✅ **实时监控**: 实时显示任务状态、PC端连接和系统日志  
✅ **Qt界面**: 现代化的图形用户界面，操作简单直观  

## 系统要求

- Windows 7/10/11 (32位或64位)
- Python 3.7+
- PyQt5
- 华视电子身份证阅读器及SDK

## 安装步骤

### 1. 安装Python依赖

```bash
pip install -r requirements.txt
```

### 2. 放置SDK文件

将华视电子SDK文件放置到 `server/` 目录下：

```
server/
├── Termb.dll          # 主要API库
├── WltRS.dll          # 照片解码库  
├── sdtapi.dll         # 安全模块通讯
├── license.dat        # 许可证文件
└── DLL_File.dll       # 其他依赖库
```

### 3. 连接读卡器

- 通过USB连接华视电子身份证阅读器
- 确保设备驱动已正确安装

## 使用方法

### 启动程序

```bash
python main.py
```

### 基本操作流程

1. **连接读卡器**
   - 点击"连接读卡器"按钮
   - 确认连接状态显示为"已连接"

2. **启动服务器**
   - 点击"启动服务器"按钮
   - 服务器将在端口9090监听PC端连接

3. **读取身份证**
   - 将身份证放置在读卡器上
   - 点击"读取身份证"按钮
   - 系统自动创建拍摄任务

4. **PC端连接**
   - PC端程序连接到服务器
   - 自动获取待处理任务
   - 完成拍照并上传证件照

5. **导出照片**
   - 点击"导出照片"按钮
   - 选择导出目录
   - 照片将按身份证号码命名导出

## 界面说明

### 主界面布局

- **左侧控制面板**: 读卡器控制、服务器控制、导出控制、统计信息
- **右侧信息面板**: 系统日志、任务列表、PC端连接状态

### 控制面板功能

#### 读卡器控制
- 连接/断开读卡器
- 读取身份证信息
- 显示连接状态

#### 服务器控制  
- 启动/停止任务服务器
- 显示服务器运行状态
- 监听PC端连接

#### 导出控制
- 导出证件照片
- 导出任务列表(CSV格式)

#### 统计信息
- 总任务数
- 待处理任务数  
- 已分配任务数
- 已完成任务数

### 信息面板

#### 系统日志
- 实时显示系统运行日志
- 记录读卡、任务分配、照片上传等操作

#### 任务列表
- 显示所有任务的详细信息
- 包括任务ID、姓名、身份证号、状态等

#### PC端连接
- 显示当前连接的PC端信息
- 包括客户端ID、IP地址、连接时间、任务数

## 数据库结构

系统使用SQLite数据库，包含一个主表：

### photo_tasks 表

| 字段 | 类型 | 说明 |
|------|------|------|
| id | INTEGER | 主键ID |
| task_id | VARCHAR(50) | 任务ID |
| pc_client_id | VARCHAR(50) | 执行任务的PC端ID |
| name | VARCHAR(30) | 姓名 |
| gender | VARCHAR(2) | 性别 |
| birth_date | VARCHAR(16) | 出生日期 |
| id_number | VARCHAR(36) | 身份证号码 |
| photo_data | BLOB | 证件照数据 |
| photo_filename | VARCHAR(255) | 证件照文件名 |
| task_status | VARCHAR(20) | 任务状态 |
| created_time | DATETIME | 创建时间 |
| assigned_time | DATETIME | 分配时间 |
| completed_time | DATETIME | 完成时间 |

### 任务状态说明

- `pending`: 待处理 - 任务已创建，等待PC端领取
- `assigned`: 已分配 - 任务已分配给PC端，正在处理
- `completed`: 已完成 - 照片已上传，任务完成

## 网络通信协议

服务器与PC端通过TCP Socket通信，使用JSON格式消息：

### PC端请求任务
```json
{
    "type": "get_task"
}
```

### 服务器响应任务
```json
{
    "status": "success",
    "task": {
        "task_id": "uuid",
        "name": "张三",
        "gender": "男", 
        "birth_date": "19900101",
        "id_number": "123456789012345678"
    },
    "client_id": "client_uuid"
}
```

### PC端上传照片
```json
{
    "type": "upload_photo",
    "task_id": "uuid",
    "photo_data": "base64_encoded_image",
    "photo_filename": "photo.jpg"
}
```

### PC端取消任务
```json
{
    "type": "cancel_task",
    "task_id": "uuid"
}
```

## 配置说明

主要配置项在 `config.py` 文件中：

```python
# 网络配置
SERVER_PORT = 9090          # 服务器监听端口
SERVER_HOST = "0.0.0.0"     # 服务器监听地址

# 读卡器配置  
CARD_READER_PORT = 1001     # 读卡器端口(USB)

# 界面配置
WINDOW_WIDTH = 1000         # 窗口宽度
WINDOW_HEIGHT = 700         # 窗口高度

# 导出配置
DEFAULT_EXPORT_DIR = "./exported_photos"  # 默认导出目录
DEFAULT_CSV_FILE = "./task_list.csv"      # 默认CSV文件
```

## 故障排除

### 读卡器连接失败
1. 检查USB连接是否正常
2. 确认设备驱动已安装
3. 检查SDK文件是否完整
4. 尝试重新插拔设备

### 服务器启动失败
1. 检查端口9090是否被占用
2. 确认防火墙设置允许程序通信
3. 查看系统日志了解详细错误信息

### PC端连接失败
1. 确认服务器已启动
2. 检查网络连接
3. 确认IP地址和端口配置正确

### 读卡失败
1. 确保身份证放置正确
2. 清洁身份证表面
3. 检查读卡器是否正常工作
4. 重新放置身份证

## 日志文件

程序运行时会生成日志文件 `id_card_server.log`，记录详细的运行信息，便于问题排查。

## 技术支持

如有问题，请查看：
1. 系统日志信息
2. 日志文件内容  
3. 华视电子SDK文档

---

**注意**: 本程序仅支持华视电子身份证阅读器，使用前请确保设备和SDK的兼容性。
