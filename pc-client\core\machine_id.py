#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器码获取工具
用于生成唯一的机器标识
"""

import hashlib
import platform
import subprocess
import logging

logger = logging.getLogger(__name__)

def get_machine_id():
    """获取机器唯一标识码"""
    try:
        # 获取多个硬件信息
        machine_info = []

        # 1. 获取CPU信息（简化版本，减少超时风险）
        try:
            if platform.system() == "Windows":
                # Windows系统获取CPU序列号，减少超时时间
                result = subprocess.run(['wmic', 'cpu', 'get', 'ProcessorId'],
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines:
                        line = line.strip()
                        if line and line != "ProcessorId" and len(line) > 5:
                            machine_info.append(f"CPU:{line}")
                            break
            else:
                # Linux/Mac系统获取CPU信息
                try:
                    with open('/proc/cpuinfo', 'r') as f:
                        for line in f:
                            if 'serial' in line.lower():
                                cpu_id = line.split(':')[1].strip()
                                if cpu_id:
                                    machine_info.append(f"CPU:{cpu_id}")
                                break
                except FileNotFoundError:
                    pass  # 文件不存在，跳过
        except Exception as e:
            logger.debug(f"获取CPU信息失败: {e}")
        
        # 2. 获取主板信息（简化版本）
        try:
            if platform.system() == "Windows":
                # Windows系统获取主板序列号，减少超时时间
                result = subprocess.run(['wmic', 'baseboard', 'get', 'SerialNumber'],
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines:
                        line = line.strip()
                        if line and line != "SerialNumber" and len(line) > 3:
                            machine_info.append(f"BOARD:{line}")
                            break
        except Exception as e:
            logger.debug(f"获取主板信息失败: {e}")
        
        # 3. 获取硬盘信息（简化版本）
        try:
            if platform.system() == "Windows":
                # Windows系统获取硬盘序列号，减少超时时间
                result = subprocess.run(['wmic', 'diskdrive', 'get', 'SerialNumber'],
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines:
                        line = line.strip()
                        if line and line != "SerialNumber" and len(line) > 5:
                            machine_info.append(f"DISK:{line}")
                            break  # 只取第一个硬盘
        except Exception as e:
            logger.debug(f"获取硬盘信息失败: {e}")
        
        # 4. 获取网卡MAC地址
        try:
            import uuid
            mac = uuid.getnode()
            mac_str = ':'.join(['{:02x}'.format((mac >> elements) & 0xff) 
                               for elements in range(0, 2*6, 2)][::-1])
            machine_info.append(f"MAC:{mac_str}")
        except Exception as e:
            logger.debug(f"获取MAC地址失败: {e}")
        
        # 5. 添加系统信息作为补充
        try:
            system_info = f"{platform.system()}:{platform.machine()}:{platform.processor()}"
            machine_info.append(f"SYS:{system_info}")
        except Exception as e:
            logger.debug(f"获取系统信息失败: {e}")
        
        # 如果没有获取到任何硬件信息，使用备用方案
        if not machine_info:
            try:
                import socket
                hostname = socket.gethostname()
                machine_info.append(f"HOST:{hostname}")

                # 添加更多备用信息
                import os
                username = os.getenv('USERNAME') or os.getenv('USER') or 'unknown'
                machine_info.append(f"USER:{username}")

                logger.warning("无法获取硬件信息，使用主机名和用户名作为标识")
            except Exception as backup_error:
                logger.error(f"备用方案也失败: {backup_error}")
                # 最后的备用方案
                machine_info.append(f"FALLBACK:{platform.node()}")
        
        # 将所有信息组合并生成哈希
        combined_info = "|".join(sorted(machine_info))
        machine_hash = hashlib.sha256(combined_info.encode('utf-8')).hexdigest()
        
        # 取前12位作为机器码
        machine_id = machine_hash[:12].upper()
        
        logger.info(f"生成机器码: {machine_id}")
        logger.debug(f"机器信息: {combined_info}")
        
        return machine_id
        
    except Exception as e:
        logger.error(f"获取机器码失败: {e}")
        # 备用方案：使用当前时间戳的哈希
        import time
        fallback_id = hashlib.md5(str(time.time()).encode()).hexdigest()[:12].upper()
        logger.warning(f"使用备用机器码: {fallback_id}")
        return fallback_id

def get_simple_machine_id():
    """获取简化的机器码（快速版本）"""
    try:
        # 只使用最基本的信息，避免耗时操作
        machine_info = []

        # 1. MAC地址
        try:
            import uuid
            mac = uuid.getnode()
            mac_str = ':'.join(['{:02x}'.format((mac >> elements) & 0xff)
                               for elements in range(0, 2*6, 2)][::-1])
            machine_info.append(f"MAC:{mac_str}")
        except:
            pass

        # 2. 主机名
        try:
            import socket
            hostname = socket.gethostname()
            machine_info.append(f"HOST:{hostname}")
        except:
            pass

        # 3. 系统信息
        try:
            system_info = f"{platform.system()}:{platform.machine()}"
            machine_info.append(f"SYS:{system_info}")
        except:
            pass

        # 4. 用户名
        try:
            import os
            username = os.getenv('USERNAME') or os.getenv('USER') or 'unknown'
            machine_info.append(f"USER:{username}")
        except:
            pass

        if machine_info:
            # 将所有信息组合并生成哈希
            combined_info = "|".join(sorted(machine_info))
            machine_hash = hashlib.sha256(combined_info.encode('utf-8')).hexdigest()
            machine_id = machine_hash[:12].upper()
            logger.info(f"生成简化机器码: {machine_id}")
            return machine_id
        else:
            # 最后的备用方案
            import time
            fallback_id = hashlib.md5(f"{time.time()}:{platform.node()}".encode()).hexdigest()[:12].upper()
            logger.warning(f"使用时间戳备用机器码: {fallback_id}")
            return fallback_id

    except Exception as e:
        logger.error(f"获取简化机器码失败: {e}")
        # 最终备用方案
        import time
        fallback_id = hashlib.md5(str(time.time()).encode()).hexdigest()[:12].upper()
        return fallback_id

def get_machine_info():
    """获取详细的机器信息（用于调试）"""
    try:
        info = {
            "system": platform.system(),
            "machine": platform.machine(),
            "processor": platform.processor(),
            "hostname": platform.node(),
            "platform": platform.platform(),
        }
        
        # 获取IP地址
        try:
            import socket
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)
            info["ip"] = local_ip
        except:
            info["ip"] = "unknown"
        
        # 获取MAC地址
        try:
            import uuid
            mac = uuid.getnode()
            mac_str = ':'.join(['{:02x}'.format((mac >> elements) & 0xff) 
                               for elements in range(0, 2*6, 2)][::-1])
            info["mac"] = mac_str
        except:
            info["mac"] = "unknown"
        
        return info
        
    except Exception as e:
        logger.error(f"获取机器信息失败: {e}")
        return {"error": str(e)}

if __name__ == "__main__":
    # 测试代码
    print("机器码:", get_machine_id())
    print("机器信息:", get_machine_info())
