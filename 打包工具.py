#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
StudentFoto 证件照系统打包工具
生成PC端和服务端独立安装包
"""

import os
import sys
import subprocess
import shutil
import zipfile
from pathlib import Path
import datetime

def check_environment():
    """检查打包环境"""
    print("🔍 检查打包环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ Python版本过低，需要3.7+")
        return False
    
    # 检查PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller: {PyInstaller.__version__}")
    except ImportError:
        print("📥 安装PyInstaller...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
    
    # 检查项目结构
    required_dirs = ["pc-client", "id_card_server"]
    for dir_name in required_dirs:
        if not Path(dir_name).exists():
            print(f"❌ 缺少目录: {dir_name}")
            return False
    
    print("✅ 环境检查通过")
    return True

def build_pc_client():
    """构建PC端"""
    print("\n🖥️  构建PC端...")
    
    os.chdir("pc-client")
    
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile", "--windowed",
        "--name", "StudentFoto-PC客户端",
        "--add-data", "ui;ui",
        "--add-data", "core;core",
        "--add-data", "config.py;.",
        "--hidden-import", "PyQt5.QtCore",
        "--hidden-import", "PyQt5.QtGui",
        "--hidden-import", "PyQt5.QtWidgets",
        "--hidden-import", "PIL.Image",
        "--clean",
        "main.py"
    ]
    
    try:
        subprocess.check_call(cmd)
        print("✅ PC端构建成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ PC端构建失败")
        return False

def build_server():
    """构建服务端"""
    print("\n🖥️  构建服务端...")

    os.chdir("../id_card_server")

    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile", "--console",
        "--name", "StudentFoto-服务端",
        "--add-data", "ui;ui",
        "--add-data", "core;core",
        "--add-data", "config.py;.",
        "--hidden-import", "PyQt5.QtCore",
        "--hidden-import", "PyQt5.QtGui",
        "--hidden-import", "PyQt5.QtWidgets",
        "--hidden-import", "sqlite3",
        "--clean",
        "main.py"
    ]

    # 添加桥接程序和相关文件
    if Path("id_card_bridge.exe").exists():
        cmd.extend(["--add-binary", "id_card_bridge.exe;."])
        print("   ✅ 包含桥接程序: id_card_bridge.exe")

    # 添加身份证读卡器SDK文件
    server_dir = Path("server")
    if server_dir.exists():
        # 添加DLL文件
        dll_files = ["DLL_File.dll", "Termb.dll", "WltRS.dll", "sdtapi.dll"]
        for dll_file in dll_files:
            dll_path = server_dir / dll_file
            if dll_path.exists():
                cmd.extend(["--add-binary", f"{dll_path};server"])
                print(f"   ✅ 包含DLL文件: {dll_file}")

        # 添加授权文件
        license_file = server_dir / "license.dat"
        if license_file.exists():
            cmd.extend(["--add-data", f"{license_file};server"])
            print("   ✅ 包含授权文件: license.dat")

        # 添加SDK说明文档
        readme_file = server_dir / "身份证阅读器SDK使用手册.md"
        if readme_file.exists():
            cmd.extend(["--add-data", f"{readme_file};server"])
            print("   ✅ 包含SDK文档: 身份证阅读器SDK使用手册.md")

    try:
        subprocess.check_call(cmd)
        print("✅ 服务端构建成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ 服务端构建失败")
        return False

def create_packages():
    """创建安装包"""
    print("\n📦 创建安装包...")
    
    os.chdir("..")
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    packages_dir = Path("packages")
    
    if packages_dir.exists():
        shutil.rmtree(packages_dir)
    packages_dir.mkdir()
    
    # PC端安装包
    pc_exe = Path("pc-client/dist/StudentFoto-PC客户端.exe")
    if pc_exe.exists():
        pc_dir = packages_dir / f"StudentFoto-PC客户端-v1.0-{timestamp}"
        pc_dir.mkdir()
        
        # 复制主程序
        shutil.copy2(pc_exe, pc_dir)
        
        # 创建使用说明
        with open(pc_dir / "使用说明.txt", 'w', encoding='utf-8') as f:
            f.write(f"""# StudentFoto PC客户端 v1.0

## 🚀 使用方法
1. 双击 StudentFoto-PC客户端.exe 启动程序
2. 点击"🔍 连接设备"连接手机进行拍照
3. 点击"连接服务端"连接服务端处理证件照

## 💻 系统要求
- Windows 7 或更高版本
- 网络连接（WiFi或有线）
- 至少2GB可用内存

## 📱 连接手机拍照
1. 确保手机和PC在同一WiFi网络
2. 启动手机端"学生拍照"应用
3. 在PC端选择手机设备或手动输入IP地址
4. 连接成功后即可拍照

## 🖥️  连接服务端处理证件照
1. 确保服务端正在运行
2. 获取服务端IP地址和端口
3. 在PC端连接服务端
4. 获得机位号后等待任务分配

## 🔧 故障排除
- 首次启动可能较慢，请耐心等待
- 如果Windows Defender报警，请选择"允许运行"
- 确保防火墙允许程序访问网络
- 检查网络连接和IP地址设置

构建时间: {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
""")
        
        # 创建启动脚本
        with open(pc_dir / "启动.bat", 'w', encoding='gbk') as f:
            f.write("""@echo off
echo 正在启动StudentFoto PC客户端...
"StudentFoto-PC客户端.exe"
""")
        
        # 创建ZIP包
        with zipfile.ZipFile(packages_dir / f"StudentFoto-PC客户端-v1.0-{timestamp}.zip", 'w') as zf:
            for file in pc_dir.rglob('*'):
                if file.is_file():
                    zf.write(file, file.relative_to(pc_dir))
        
        print(f"✅ PC端安装包: StudentFoto-PC客户端-v1.0-{timestamp}.zip")
    
    # 服务端安装包
    server_exe = Path("id_card_server/dist/StudentFoto-服务端.exe")
    if server_exe.exists():
        server_dir = packages_dir / f"StudentFoto-服务端-v1.0-{timestamp}"
        server_dir.mkdir()
        
        # 复制主程序
        shutil.copy2(server_exe, server_dir)
        
        # 复制桥接程序（如果存在）
        bridge_exe = Path("id_card_server/id_card_bridge.exe")
        if bridge_exe.exists():
            shutil.copy2(bridge_exe, server_dir)
            print(f"   ✅ 复制桥接程序: {bridge_exe.name}")

        # 创建server子目录用于SDK文件
        server_sdk_dir = server_dir / "server"
        server_sdk_dir.mkdir(exist_ok=True)

        # 复制身份证读卡器SDK文件
        sdk_source_dir = Path("id_card_server/server")
        if sdk_source_dir.exists():
            # 复制DLL文件
            dll_files = ["DLL_File.dll", "Termb.dll", "WltRS.dll", "sdtapi.dll"]
            for dll_file in dll_files:
                src_dll = sdk_source_dir / dll_file
                if src_dll.exists():
                    shutil.copy2(src_dll, server_sdk_dir)
                    print(f"   ✅ 复制DLL文件: {dll_file}")

            # 复制授权文件
            license_file = sdk_source_dir / "license.dat"
            if license_file.exists():
                shutil.copy2(license_file, server_sdk_dir)
                print("   ✅ 复制授权文件: license.dat")

            # 复制SDK文档
            readme_file = sdk_source_dir / "身份证阅读器SDK使用手册.md"
            if readme_file.exists():
                shutil.copy2(readme_file, server_sdk_dir)
                print("   ✅ 复制SDK文档: 身份证阅读器SDK使用手册.md")

        # 复制配置文件
        config_files = ["id_card_server/photo_settings.json"]
        for config_file in config_files:
            src = Path(config_file)
            if src.exists():
                shutil.copy2(src, server_dir)
                print(f"   ✅ 复制配置文件: {src.name}")
        
        # 创建使用说明
        with open(server_dir / "使用说明.txt", 'w', encoding='utf-8') as f:
            f.write(f"""# StudentFoto 服务端 v1.0

## 🚀 使用方法
1. 双击 StudentFoto-服务端.exe 启动程序
2. 查看程序显示的IP地址和端口号
3. 等待PC端连接并分配机位号
4. 连接身份证读卡器开始工作

## 💻 系统要求
- Windows 7 或更高版本
- 至少4GB可用内存
- 身份证读卡器及驱动（可选）
- 网络连接

## 🔧 功能说明
- 身份证读卡器集成
- PC端连接管理
- 任务分配和调度
- 证件照处理
- 数据库存储

## 📁 文件说明
- StudentFoto-服务端.exe: 主程序
- id_card_bridge.exe: 身份证读卡器桥接程序（32位）
- photo_settings.json: 证件照处理设置
- server/: 身份证读卡器SDK文件夹
  - *.dll: 华视电子身份证读卡器SDK动态库
  - license.dat: SDK授权文件
  - 身份证阅读器SDK使用手册.md: SDK使用说明

## 🔧 故障排除
- 首次运行会自动创建数据库
- 确保防火墙允许程序访问网络
- 检查身份证读卡器连接和驱动
- 查看程序日志排查问题

## 📋 网络配置
- 默认端口: 9090
- 确保PC端能访问此端口
- 记录IP地址供PC端连接使用

构建时间: {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
""")
        
        # 创建启动脚本
        with open(server_dir / "启动服务端.bat", 'w', encoding='gbk') as f:
            f.write("""@echo off
echo 正在启动StudentFoto服务端...
echo 请记录显示的IP地址和端口号
echo 供PC端连接使用
echo.
"StudentFoto-服务端.exe"
pause
""")
        
        # 创建ZIP包
        with zipfile.ZipFile(packages_dir / f"StudentFoto-服务端-v1.0-{timestamp}.zip", 'w') as zf:
            for file in server_dir.rglob('*'):
                if file.is_file():
                    zf.write(file, file.relative_to(server_dir))
        
        print(f"✅ 服务端安装包: StudentFoto-服务端-v1.0-{timestamp}.zip")
    
    return True

def main():
    """主函数"""
    print("StudentFoto 证件照系统打包工具")
    print("="*50)
    print("将生成两个独立的安装包:")
    print("1. PC端安装包 - 用于拍照工作站")
    print("2. 服务端安装包 - 用于服务器/主控PC")
    print("="*50)
    
    if not check_environment():
        print("❌ 环境检查失败")
        return False
    
    print("\n🚀 开始打包...")
    
    success = True
    
    # 构建PC端
    if not build_pc_client():
        success = False
    
    # 构建服务端
    if not build_server():
        success = False
    
    # 创建安装包
    if success:
        create_packages()
        
        print("\n" + "="*50)
        print("🎉 打包完成！")
        print("📁 安装包位于 packages/ 目录")
        print("\n📦 生成的安装包:")
        
        packages_dir = Path("packages")
        for zip_file in packages_dir.glob("*.zip"):
            size_mb = zip_file.stat().st_size / (1024 * 1024)
            print(f"   📄 {zip_file.name} ({size_mb:.1f}MB)")
        
        print("\n📋 部署说明:")
        print("1. 先部署服务端安装包到服务器")
        print("2. 再部署PC端安装包到各拍照工作站")
        print("3. 确保所有设备在同一网络环境")
        print("="*50)
    else:
        print("\n❌ 打包失败！请检查错误信息")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            input("\n按回车键退出...")
            sys.exit(1)
        else:
            input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n用户中断打包")
    except Exception as e:
        print(f"\n❌ 打包异常: {e}")
        input("\n按回车键退出...")
        sys.exit(1)
