cmake_minimum_required(VERSION 3.10)
project(IDCardBridge)

# 设置C++标准
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 强制32位编译
set(CMAKE_GENERATOR_PLATFORM Win32)

# 查找jsoncpp库
find_package(PkgConfig REQUIRED)
pkg_check_modules(JSONC<PERSON> jsoncpp)

# 如果找不到jsoncpp，使用vcpkg或手动指定
if(NOT JSONCPP_FOUND)
    # 尝试使用vcpkg
    find_path(JSONCPP_INCLUDE_DIR json/json.h)
    find_library(JSONCPP_LIBRARY jsoncpp)
    
    if(JSONCPP_INCLUDE_DIR AND JSONCPP_LIBRARY)
        set(JSONCPP_FOUND TRUE)
        set(JSONCPP_INCLUDE_DIRS ${JSONCPP_INCLUDE_DIR})
        set(JSONCPP_LIBRARIES ${JSONCPP_LIBRARY})
    endif()
endif()

# 创建可执行文件
add_executable(id_card_bridge id_card_bridge.cpp)

# 链接库
target_link_libraries(id_card_bridge ws2_32)

# 如果找到jsoncpp，链接它
if(JSONCPP_FOUND)
    target_include_directories(id_card_bridge PRIVATE ${JSONCPP_INCLUDE_DIRS})
    target_link_libraries(id_card_bridge ${JSONCPP_LIBRARIES})
else()
    message(WARNING "jsoncpp not found. You may need to install it manually.")
endif()

# 设置输出目录
set_target_properties(id_card_bridge PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
)
