# 身份证读卡器服务端项目

## 🎯 项目概述

这是一个基于华视电子身份证阅读器SDK开发的服务端程序，使用PyQt5实现图形界面，支持身份证信息读取、任务管理和照片导出功能。

## 📁 项目结构

```
id_card_server/
├── main.py                   # 主程序入口
├── config.py                 # 配置文件
├── requirements.txt          # Python依赖
├── start.bat                 # Windows启动脚本
├── README.md                 # 项目文档
├── 项目说明.md               # 中文说明文档
├── core/                     # 核心模块
│   ├── __init__.py
│   ├── id_card_reader.py     # 身份证读卡器接口
│   ├── database.py           # 数据库管理
│   ├── task_manager.py       # 任务管理和网络通信
│   └── photo_exporter.py     # 照片导出功能
├── ui/                       # 用户界面
│   ├── __init__.py
│   ├── main_window.py        # 主窗口界面
│   └── export_dialog.py      # 导出对话框
└── server/                   # SDK文件目录
    ├── README.md             # SDK文件说明
    ├── Termb.dll             # 主要API库(需要放置)
    ├── WltRS.dll             # 照片解码库(需要放置)
    ├── sdtapi.dll            # 安全模块通讯(需要放置)
    ├── license.dat           # 许可证文件(需要放置)
    └── DLL_File.dll          # 其他依赖库(需要放置)
```

## 🔧 核心功能模块

### 1. 身份证读卡器接口 (id_card_reader.py)
- 封装华视电子SDK的DLL调用
- 支持读卡器连接/断开
- 读取身份证基本信息(姓名、性别、出生日期、身份证号)
- 错误处理和日志记录

### 2. 数据库管理 (database.py)
- 使用SQLite数据库
- 单表设计，包含任务ID、PC端ID、身份证信息、照片数据
- 支持任务创建、查询、更新、删除
- 统计信息查询

### 3. 任务管理器 (task_manager.py)
- TCP服务器，监听PC端连接
- JSON格式消息通信
- 任务分配和状态管理
- 照片上传处理
- 实时状态信号发送

### 4. 照片导出器 (photo_exporter.py)
- 按身份证号码导出照片
- 支持批量导出
- 进度显示和错误处理
- CSV格式任务列表导出

### 5. 主窗口界面 (main_window.py)
- 现代化Qt界面设计
- 实时状态监控
- 操作日志显示
- 任务列表和PC端连接管理

### 6. 导出对话框 (export_dialog.py)
- 导出设置界面
- 进度显示
- 统计信息展示

## 🗄️ 数据库设计

### photo_tasks 表 (单表设计)

| 字段名 | 数据类型 | 说明 |
|--------|----------|------|
| id | INTEGER | 主键ID |
| task_id | VARCHAR(50) | 唯一任务ID |
| pc_client_id | VARCHAR(50) | 执行任务的PC端ID |
| name | VARCHAR(30) | 姓名 |
| gender | VARCHAR(2) | 性别 |
| birth_date | VARCHAR(16) | 出生日期 |
| id_number | VARCHAR(36) | 身份证号码 |
| photo_data | BLOB | 证件照二进制数据 |
| photo_filename | VARCHAR(255) | 照片文件名 |
| task_status | VARCHAR(20) | 任务状态 |
| created_time | DATETIME | 创建时间 |
| assigned_time | DATETIME | 分配时间 |
| completed_time | DATETIME | 完成时间 |

### 任务状态流转

```
pending (待处理) → assigned (已分配) → completed (已完成)
                                   ↓
                              cancelled (已取消) → 删除记录
```

## 🌐 网络通信协议

### 消息格式
所有消息使用JSON格式，通过TCP Socket传输。

### 主要消息类型

#### 1. 获取任务
**请求**:
```json
{"type": "get_task"}
```

**响应**:
```json
{
    "status": "success",
    "task": {
        "task_id": "uuid",
        "name": "张三",
        "gender": "男",
        "birth_date": "19900101", 
        "id_number": "123456789012345678"
    },
    "client_id": "client_uuid"
}
```

#### 2. 上传照片
**请求**:
```json
{
    "type": "upload_photo",
    "task_id": "uuid",
    "photo_data": "base64_encoded_image",
    "photo_filename": "photo.jpg"
}
```

#### 3. 取消任务
**请求**:
```json
{
    "type": "cancel_task",
    "task_id": "uuid"
}
```

## 🚀 部署和使用

### 环境要求
- Windows 7/10/11
- Python 3.7+
- PyQt5
- 华视电子身份证阅读器

### 安装步骤
1. 安装Python依赖: `pip install -r requirements.txt`
2. 将SDK文件放置到server目录
3. 连接身份证读卡器
4. 运行: `python main.py` 或双击 `start.bat`

### 使用流程
1. 连接读卡器 → 启动服务器
2. 读取身份证 → 创建任务
3. PC端连接 → 获取任务 → 拍照上传
4. 导出照片 → 按身份证号命名

## ✨ 特色功能

### 1. 单表设计
- 简化数据库结构
- 任务取消直接删除记录
- 照片直接存储在数据库中

### 2. 实时监控
- 任务状态实时更新
- PC端连接状态监控
- 详细操作日志

### 3. 多PC端支持
- 支持多个PC端同时连接
- 自动任务分配
- 客户端标识管理

### 4. 照片管理
- 仅保存证件照，不保存原始照片
- 按身份证号码导出
- 批量导出功能

### 5. 用户友好界面
- 现代化Qt界面
- 直观的状态显示
- 简单的操作流程

## 🔍 技术亮点

1. **模块化设计**: 核心功能分离，便于维护和扩展
2. **异步处理**: 网络通信和界面操作分离，避免界面卡顿
3. **信号机制**: 使用Qt信号槽实现组件间通信
4. **错误处理**: 完善的异常处理和日志记录
5. **配置管理**: 集中配置管理，便于部署调整

## 📝 开发说明

### 代码规范
- 使用中文注释和文档字符串
- 遵循PEP 8代码风格
- 类型提示增强代码可读性

### 扩展建议
1. 添加用户权限管理
2. 支持更多证件类型
3. 增加数据备份功能
4. 添加远程管理接口

---

**注意**: 使用前请确保华视电子SDK文件完整，并且读卡器设备正常连接。
