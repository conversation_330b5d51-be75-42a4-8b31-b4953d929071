<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- 设备名称设置 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="设备设置"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="#333333"
        android:layout_marginBottom="12dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="设备名: "
            android:textSize="14sp" />

        <EditText
            android:id="@+id/device_name_edit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:hint="输入设备名称"
            android:textSize="14sp"
            android:maxLines="1"
            android:inputType="text"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/save_device_name_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="保存"
            android:textSize="12sp" />

    </LinearLayout>

    <!-- 分隔线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#E0E0E0"
        android:layout_marginBottom="16dp" />

    <!-- 状态信息 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="状态信息"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="#333333"
        android:layout_marginBottom="12dp" />

    <TextView
        android:id="@+id/ip_address_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="IP地址: 加载中..."
        android:textSize="14sp"
        android:textColor="#666666"
        android:layout_marginBottom="8dp" />

    <TextView
        android:id="@+id/screen_status_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="🔆 屏幕保持常亮"
        android:textSize="14sp"
        android:textColor="#4CAF50"
        android:layout_marginBottom="8dp" />

    <TextView
        android:id="@+id/broadcast_status_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="📡 广播: 已启动"
        android:textSize="14sp"
        android:textColor="#4CAF50"
        android:layout_marginBottom="8dp" />

    <TextView
        android:id="@+id/orientation_status_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="🧭 方向: 竖屏"
        android:textSize="14sp"
        android:textColor="#2196F3" />

</LinearLayout>
