# 学生拍照系统 - Android端应用

这是一个用于接收PC端远程控制进行拍照的Android应用，使用Kotlin和CameraX开发。

## 功能特性

- 📱 **相机控制**: 使用CameraX提供稳定的相机功能
- 🔗 **网络通信**: 通过WiFi接收PC端的拍照指令
- 📸 **远程拍照**: 响应PC端命令进行拍照
- 🔄 **自动旋转**: 根据设备方向自动校正照片方向
- 📡 **实时状态**: 显示连接状态和设备方向
- 🔋 **屏幕常亮**: 防止拍照过程中屏幕休眠
- 🛡️ **权限管理**: 自动请求相机和存储权限

## 系统要求

- Android 7.0 (API level 24) 或更高版本
- 相机权限
- 存储权限
- 网络权限

## 安装步骤

### 方法一：直接安装APK
1. 下载最新的APK文件
2. 在Android设备上启用"未知来源"安装
3. 安装APK文件

### 方法二：从源码构建
1. **克隆项目**
   ```bash
   git clone <项目地址>
   cd studentfoto
   ```

2. **使用Android Studio**
   - 打开Android Studio
   - 选择"Open an existing project"
   - 选择项目根目录
   - 等待Gradle同步完成

3. **构建和安装**
   - 连接Android设备或启动模拟器
   - 点击"Run"按钮或使用快捷键Ctrl+R

## 使用说明

### 1. 首次启动

1. **授予权限**
   - 启动应用后会自动请求相机权限
   - 授予相机权限以正常使用拍照功能
   - 授予存储权限以保存照片

2. **查看网络信息**
   - 应用启动后会显示当前设备的IP地址
   - 确保设备连接到WiFi网络
   - 记录显示的IP地址，用于PC端连接

### 2. 连接PC端

1. **确保网络连接**
   - Android设备和PC必须连接到同一个WiFi网络
   - 应用会在端口8080上监听连接

2. **等待PC端连接**
   - 应用启动后会自动开始监听
   - 连接状态会在界面上显示
   - 当PC端连接成功时，状态会更新为"PC已连接"

### 3. 拍照操作

1. **接收拍照指令**
   - PC端发送拍照命令后，手机会显示"收到拍照指令"提示
   - 应用会自动进行拍照

2. **照片处理**
   - 拍照完成后，照片会根据当前设备方向自动旋转
   - 处理后的照片会发送给PC端
   - 照片同时保存在手机的相册中

### 4. 设备方向

- **方向检测**: 应用使用重力感应器检测设备方向
- **支持方向**: 竖屏、左横屏、右横屏、倒立
- **自动校正**: 照片会根据拍摄时的设备方向自动校正
- **状态显示**: 当前设备方向会在界面上实时显示

## 界面说明

### 主界面元素

1. **相机预览**: 占据大部分屏幕的相机预览区域
2. **状态栏**: 显示以下信息
   - 📱 连接状态（等待PC连接/PC已连接）
   - 🧭 设备方向（竖屏/左横屏/右横屏/倒立）
   - 📶 网络信息（IP地址）

### 状态指示

- **绿色**: 正常状态
- **黄色**: 等待连接
- **红色**: 错误状态

## 网络配置

- **监听端口**: 8080
- **通信协议**: TCP Socket
- **数据格式**: 文本命令 + 二进制照片数据

## 故障排除

### 连接问题

1. **PC端无法连接**
   - 检查Android设备和PC是否在同一WiFi网络
   - 确认防火墙没有阻止8080端口
   - 重启应用重新监听端口
   - 检查IP地址是否正确

2. **连接频繁断开**
   - 检查WiFi信号强度
   - 确保设备不会自动休眠
   - 检查网络稳定性

### 拍照问题

1. **拍照失败**
   - 确认已授予相机权限
   - 检查相机是否被其他应用占用
   - 重启应用重新初始化相机
   - 查看错误日志获取详细信息

2. **照片方向错误**
   - 检查重力感应器是否正常工作
   - 确认设备方向检测功能正常
   - 重新校准设备方向

3. **照片传输失败**
   - 检查网络连接稳定性
   - 确认存储空间充足
   - 重新建立连接

### 权限问题

1. **相机权限被拒绝**
   - 进入系统设置 > 应用管理 > 学生拍照
   - 手动开启相机权限
   - 重启应用

2. **存储权限被拒绝**
   - 进入系统设置 > 应用管理 > 学生拍照
   - 手动开启存储权限
   - 重启应用

## 技术特性

### 相机功能
- **CameraX**: 使用Google推荐的CameraX库
- **自动对焦**: 支持触摸对焦和自动对焦
- **图像稳定**: 内置图像稳定功能
- **多分辨率**: 支持多种拍照分辨率

### 网络通信
- **TCP Socket**: 稳定的TCP连接
- **异步处理**: 非阻塞的网络通信
- **错误重试**: 自动重连机制
- **数据完整性**: 确保照片数据完整传输

### 设备适配
- **多屏幕**: 适配不同屏幕尺寸
- **多方向**: 支持所有设备方向
- **性能优化**: 内存和电池优化
- **兼容性**: 兼容Android 7.0+

## 开发说明

### 项目结构
```
app/src/main/
├── java/com/example/studentfoto/
│   └── MainActivity.kt          # 主Activity
├── res/
│   ├── layout/
│   │   └── activity_main.xml    # 主界面布局
│   ├── values/
│   │   ├── strings.xml          # 字符串资源
│   │   └── colors.xml           # 颜色资源
│   └── xml/
│       └── network_security_config.xml  # 网络安全配置
└── AndroidManifest.xml          # 应用清单
```

### 主要依赖
- **CameraX**: 相机功能
- **Kotlin Coroutines**: 异步处理
- **Material Design**: UI组件

### 构建配置
- **Target SDK**: 34 (Android 14)
- **Min SDK**: 24 (Android 7.0)
- **Compile SDK**: 34

## 许可证

本项目采用 MIT 许可证。

## 联系方式

如有问题或建议，请联系开发团队。
