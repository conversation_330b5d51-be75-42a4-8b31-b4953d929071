#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
身份证读卡器接口
"""

import ctypes
from ctypes import c_int, c_char_p, POINTER, byref, create_string_buffer
import os
import logging
import struct
import socket
import json
import subprocess
import time

logger = logging.getLogger(__name__)

def check_dll_architecture(dll_path):
    """检查DLL文件的架构（32位或64位）"""
    try:
        with open(dll_path, 'rb') as f:
            # 读取DOS头
            dos_header = f.read(64)
            if len(dos_header) < 64:
                return "未知"

            # 检查DOS签名
            if dos_header[:2] != b'MZ':
                return "不是有效的PE文件"

            # 获取PE头偏移
            pe_offset = struct.unpack('<L', dos_header[60:64])[0]

            # 跳转到PE头
            f.seek(pe_offset)
            pe_header = f.read(24)

            if len(pe_header) < 24:
                return "未知"

            # 检查PE签名
            if pe_header[:4] != b'PE\x00\x00':
                return "不是有效的PE文件"

            # 获取机器类型
            machine_type = struct.unpack('<H', pe_header[4:6])[0]

            if machine_type == 0x014c:  # IMAGE_FILE_MACHINE_I386
                return "32位"
            elif machine_type == 0x8664:  # IMAGE_FILE_MACHINE_AMD64
                return "64位"
            elif machine_type == 0x0200:  # IMAGE_FILE_MACHINE_IA64
                return "64位 (IA64)"
            else:
                return f"未知架构 (0x{machine_type:04x})"

    except Exception as e:
        return f"检查失败: {e}"

class IDCardReader:
    """身份证读卡器接口"""
    
    def __init__(self):
        self.dll = None
        self.is_connected = False
        self.use_bridge = False
        self.bridge_socket = None
        self.bridge_process = None
        self.auto_read_enabled = False
        self.last_card_present = False
        self.load_dll()
    
    def load_dll(self):
        """加载DLL库"""
        try:
            # 获取当前脚本所在目录
            current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            dll_path = os.path.join(current_dir, "server", "Termb.dll")

            logger.info(f"尝试加载DLL: {dll_path}")
            logger.info(f"文件存在: {os.path.exists(dll_path)}")

            if not os.path.exists(dll_path):
                raise FileNotFoundError(f"DLL文件不存在: {dll_path}")

            # 检查DLL架构
            dll_arch = check_dll_architecture(dll_path)
            python_arch = "64位" if struct.calcsize("P") == 8 else "32位"

            logger.info(f"DLL架构: {dll_arch}")
            logger.info(f"Python架构: {python_arch}")

            if dll_arch == "32位" and python_arch == "64位":
                logger.warning("检测到架构不匹配：32位DLL + 64位Python")
                logger.info("启用桥接模式...")

                # 启用桥接模式
                self.use_bridge = True
                self._start_bridge()
                return

            elif dll_arch == "64位" and python_arch == "32位":
                logger.warning("检测到架构不匹配：64位DLL + 32位Python")
                self.dll = ctypes.WinDLL(dll_path)
            else:
                # 架构匹配，直接加载
                self.dll = ctypes.WinDLL(dll_path)
            
            # 定义函数原型
            self.dll.CVR_InitComm.argtypes = [c_int]
            self.dll.CVR_InitComm.restype = c_int
            
            self.dll.CVR_Authenticate.argtypes = []
            self.dll.CVR_Authenticate.restype = c_int
            
            self.dll.CVR_Read_FPContent.argtypes = []
            self.dll.CVR_Read_FPContent.restype = c_int
            
            self.dll.CVR_CloseComm.argtypes = []
            self.dll.CVR_CloseComm.restype = c_int
            
            self.dll.GetPeopleName.argtypes = [c_char_p, POINTER(c_int)]
            self.dll.GetPeopleName.restype = c_int
            
            self.dll.GetPeopleSex.argtypes = [c_char_p, POINTER(c_int)]
            self.dll.GetPeopleSex.restype = c_int
            
            self.dll.GetPeopleBirthday.argtypes = [c_char_p, POINTER(c_int)]
            self.dll.GetPeopleBirthday.restype = c_int
            
            self.dll.GetPeopleIDCode.argtypes = [c_char_p, POINTER(c_int)]
            self.dll.GetPeopleIDCode.restype = c_int
            
            logger.info("DLL库加载成功")
            
        except Exception as e:
            error_msg = f"加载DLL失败: {e}"
            if "193" in str(e):
                error_msg += "\n可能原因：DLL架构不匹配（32位/64位）"
            logger.error(error_msg)
            raise Exception(error_msg)

    def _start_bridge(self):
        """启动桥接进程"""
        try:
            # 获取桥接程序路径
            current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            bridge_exe = os.path.join(current_dir, "id_card_bridge.exe")

            # 检查桥接程序是否存在
            if not os.path.exists(bridge_exe):
                logger.error(f"桥接程序不存在: {bridge_exe}")
                logger.info("请运行 build_bridge.bat 编译桥接程序")
                raise FileNotFoundError(f"桥接程序不存在: {bridge_exe}")

            # 启动桥接进程
            logger.info(f"启动桥接程序: {bridge_exe}")
            self.bridge_process = subprocess.Popen([bridge_exe], cwd=current_dir)

            # 等待桥接服务器启动
            logger.info("等待桥接服务器启动...")
            time.sleep(3)

            # 连接到桥接服务器
            self.bridge_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.bridge_socket.connect(('localhost', 9091))

            logger.info("桥接模式启动成功")

        except Exception as e:
            logger.error(f"启动桥接模式失败: {e}")
            raise e

    def _send_bridge_command(self, command, **kwargs):
        """发送桥接命令"""
        if not self.use_bridge or not self.bridge_socket:
            return None

        try:
            request = {'command': command, **kwargs}
            self.bridge_socket.send(json.dumps(request).encode('utf-8'))

            response_data = self.bridge_socket.recv(4096).decode('utf-8')
            response = json.loads(response_data)

            return response

        except Exception as e:
            logger.error(f"桥接命令失败: {e}")
            return None
    
    def connect(self, port=1001):
        """连接读卡器"""
        if self.use_bridge:
            # 桥接模式
            # 检查桥接进程是否存在，如果不存在则重新启动
            if not self.bridge_process or self.bridge_process.poll() is not None:
                logger.info("桥接进程不存在，重新启动...")
                try:
                    self._start_bridge()
                except Exception as e:
                    logger.error(f"重新启动桥接进程失败: {e}")
                    return False

            # 检查Socket连接是否存在
            if not self.bridge_socket:
                try:
                    import socket
                    self.bridge_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    self.bridge_socket.connect(('localhost', 9091))
                    logger.info("重新连接到桥接服务器")
                except Exception as e:
                    logger.error(f"重新连接桥接服务器失败: {e}")
                    return False

            response = self._send_bridge_command('connect', port=port)
            if response and response.get('success'):
                self.is_connected = True
                logger.info(f"读卡器连接成功（桥接模式），端口: {port}")
                return True
            else:
                logger.error("读卡器连接失败（桥接模式）")
                return False
        else:
            # 直接模式
            try:
                result = self.dll.CVR_InitComm(port)
                if result == 1:
                    self.is_connected = True
                    logger.info(f"读卡器连接成功，端口: {port}")
                    return True
                else:
                    logger.error(f"读卡器连接失败，返回值: {result}")
                    return False
            except Exception as e:
                logger.error(f"连接读卡器异常: {e}")
                return False
    
    def disconnect(self):
        """断开连接"""
        if self.use_bridge:
            # 桥接模式
            if self.is_connected:
                response = self._send_bridge_command('disconnect')
                self.is_connected = False

                logger.info("读卡器连接已断开（桥接模式）")
                return response and response.get('success', True)
            return True
        else:
            # 直接模式
            try:
                if self.is_connected:
                    result = self.dll.CVR_CloseComm()
                    self.is_connected = False
                    logger.info("读卡器连接已断开")
                    return result == 1
                return True
            except Exception as e:
                logger.error(f"断开连接异常: {e}")
                return False

    def close_bridge(self):
        """完全关闭桥接进程（程序退出时调用）"""
        if self.use_bridge:
            # 先断开读卡器连接
            if self.is_connected:
                self.disconnect()

            # 关闭桥接Socket连接
            if self.bridge_socket:
                try:
                    self.bridge_socket.close()
                except:
                    pass
                self.bridge_socket = None

            # 终止桥接进程
            if self.bridge_process:
                try:
                    self.bridge_process.terminate()
                    self.bridge_process.wait(timeout=5)
                except:
                    pass
                self.bridge_process = None

            logger.info("桥接进程已完全关闭")

    def check_card_present(self):
        """检查是否有卡片存在"""
        if self.use_bridge:
            # 桥接模式
            if not self.is_connected:
                return False

            response = self._send_bridge_command('check_card')
            if response and response.get('success'):
                return response.get('card_present', False)
            return False
        else:
            # 直接模式
            if not self.is_connected or not self.dll:
                return False

            try:
                result = self.dll.CVR_Authenticate()
                return result == 1  # 1表示有卡
            except Exception as e:
                logger.error(f"检查卡片存在异常: {e}")
                return False

    def enable_auto_read(self, enabled=True):
        """启用/禁用自动读卡"""
        self.auto_read_enabled = enabled
        logger.info(f"自动读卡: {'启用' if enabled else '禁用'}")

    def check_card_status_changed(self):
        """检查卡片状态是否发生变化"""
        current_card_present = self.check_card_present()

        # 检查是否从无卡变为有卡（新卡插入）
        if not self.last_card_present and current_card_present:
            self.last_card_present = current_card_present
            return True, "card_inserted"

        # 检查是否从有卡变为无卡（卡片移除）
        elif self.last_card_present and not current_card_present:
            self.last_card_present = current_card_present
            return True, "card_removed"

        # 状态无变化
        self.last_card_present = current_card_present
        return False, None
    
    def authenticate_card(self):
        """卡认证"""
        if not self.is_connected:
            logger.warning("读卡器未连接")
            return False
        
        try:
            result = self.dll.CVR_Authenticate()
            return result == 1
        except Exception as e:
            logger.error(f"卡认证异常: {e}")
            return False
    
    def read_card_info(self):
        """读取身份证信息"""
        if self.use_bridge:
            # 桥接模式
            if not self.is_connected:
                error_msg = "读卡器未连接"
                logger.warning(error_msg)
                return None, error_msg

            response = self._send_bridge_command('read_card')
            if response and response.get('success'):
                card_info = response.get('data')
                if card_info:
                    logger.info(f"成功读取身份证（桥接模式）: {card_info['name']} ({card_info['id_number']})")
                    return card_info, None

            # 获取具体错误信息
            error_msg = "读卡失败（桥接模式）"
            if response:
                if not response.get('success'):
                    error_msg = response.get('error', '未知错误')
                elif not response.get('data'):
                    error_msg = "未获取到卡片数据"
            else:
                error_msg = "桥接通信失败"

            logger.warning(error_msg)
            return None, error_msg
        else:
            # 直接模式
            if not self.authenticate_card():
                error_msg = "卡认证失败"
                logger.warning(error_msg)
                return None, error_msg

            try:
                # 读取卡片内容
                result = self.dll.CVR_Read_FPContent()
                if result != 1:
                    error_msg = f"读卡失败，返回值: {result}"
                    logger.error(error_msg)
                    return None, error_msg

                # 获取各项信息
                card_info = {
                    'name': self._get_name(),
                    'gender': self._get_gender(),
                    'birth_date': self._get_birth_date(),
                    'id_number': self._get_id_number()
                }

                # 验证必要字段
                if not all([card_info['name'], card_info['gender'],
                           card_info['birth_date'], card_info['id_number']]):
                    error_msg = "读取的身份证信息不完整"
                    logger.error(error_msg)
                    return None, error_msg

                logger.info(f"成功读取身份证: {card_info['name']} ({card_info['id_number']})")
                return card_info, None

            except Exception as e:
                error_msg = f"读取身份证信息异常: {e}"
                logger.error(error_msg)
                return None, error_msg
    
    def _get_name(self):
        """获取姓名"""
        try:
            buffer = create_string_buffer(100)
            length = c_int(100)
            result = self.dll.GetPeopleName(buffer, byref(length))
            if result == 1:
                return buffer.value.decode('gbk', errors='ignore').strip()
            return ""
        except Exception as e:
            logger.error(f"获取姓名失败: {e}")
            return ""
    
    def _get_gender(self):
        """获取性别"""
        try:
            buffer = create_string_buffer(10)
            length = c_int(10)
            result = self.dll.GetPeopleSex(buffer, byref(length))
            if result == 1:
                return buffer.value.decode('gbk', errors='ignore').strip()
            return ""
        except Exception as e:
            logger.error(f"获取性别失败: {e}")
            return ""
    
    def _get_birth_date(self):
        """获取出生日期"""
        try:
            buffer = create_string_buffer(20)
            length = c_int(20)
            result = self.dll.GetPeopleBirthday(buffer, byref(length))
            if result == 1:
                return buffer.value.decode('gbk', errors='ignore').strip()
            return ""
        except Exception as e:
            logger.error(f"获取出生日期失败: {e}")
            return ""
    
    def _get_id_number(self):
        """获取身份证号码"""
        try:
            buffer = create_string_buffer(50)
            length = c_int(50)
            result = self.dll.GetPeopleIDCode(buffer, byref(length))
            if result == 1:
                return buffer.value.decode('gbk', errors='ignore').strip()
            return ""
        except Exception as e:
            logger.error(f"获取身份证号码失败: {e}")
            return ""
    
    def test_connection(self):
        """测试连接"""
        if not self.is_connected:
            return False

        try:
            # 简单的状态检查
            return True
        except Exception as e:
            logger.error(f"测试连接失败: {e}")
            return False

    def __del__(self):
        """析构函数"""
        self.disconnect()
