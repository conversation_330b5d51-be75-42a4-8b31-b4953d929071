#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PC端程序打包脚本
使用PyInstaller将Python程序打包为Windows可执行文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print(f"✅ PyInstaller已安装，版本: {PyInstaller.__version__}")
        return True
    except ImportError:
        print("❌ PyInstaller未安装")
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    print("正在安装PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ PyInstaller安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ PyInstaller安装失败")
        return False

def create_spec_file():
    """创建PyInstaller规格文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui', 
        'PyQt5.QtWidgets',
        'PIL.Image',
        'PIL.ImageDraw',
        'PIL.ImageFont',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='StudentFoto-PC客户端',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)
'''
    
    with open('studentfoto.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    print("✅ 创建规格文件: studentfoto.spec")

def build_executable():
    """构建可执行文件"""
    print("开始构建可执行文件...")
    try:
        # 使用规格文件构建
        subprocess.check_call([
            sys.executable, "-m", "PyInstaller", 
            "--clean",  # 清理临时文件
            "studentfoto.spec"
        ])
        print("✅ 构建完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        return False

def create_distribution():
    """创建发布包"""
    dist_dir = Path("dist")
    if not dist_dir.exists():
        print("❌ dist目录不存在，构建可能失败")
        return False
    
    exe_file = dist_dir / "StudentFoto-PC客户端.exe"
    if not exe_file.exists():
        print("❌ 可执行文件不存在")
        return False
    
    # 创建发布目录
    release_dir = Path("release")
    if release_dir.exists():
        shutil.rmtree(release_dir)
    release_dir.mkdir()
    
    # 复制可执行文件
    shutil.copy2(exe_file, release_dir)
    
    # 创建使用说明
    readme_content = """# StudentFoto PC客户端

## 使用说明

1. 双击 `StudentFoto-PC客户端.exe` 启动程序
2. 点击 "🔍 发现并连接设备" 按钮
3. 选择要连接的Android设备
4. 连接成功后即可进行拍照操作

## 系统要求

- Windows 7 或更高版本
- 与Android设备在同一网络环境下

## 注意事项

- 首次运行可能需要较长时间启动
- 如果Windows Defender报警，请选择"允许运行"
- 确保防火墙允许程序访问网络

## 版本信息

版本: 1.0
构建时间: {build_time}
"""
    
    import datetime
    build_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    with open(release_dir / "使用说明.txt", 'w', encoding='utf-8') as f:
        f.write(readme_content.format(build_time=build_time))
    
    print(f"✅ 发布包创建完成: {release_dir.absolute()}")
    print(f"📁 可执行文件: {exe_file.name}")
    print(f"📄 使用说明: 使用说明.txt")
    
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("StudentFoto PC客户端打包工具")
    print("=" * 50)
    
    # 检查当前目录
    if not Path("main.py").exists():
        print("❌ 请在pc-client目录下运行此脚本")
        return False
    
    # 检查并安装PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            return False
    
    # 创建规格文件
    create_spec_file()
    
    # 构建可执行文件
    if not build_executable():
        return False
    
    # 创建发布包
    if not create_distribution():
        return False
    
    print("\n" + "=" * 50)
    print("🎉 打包完成！")
    print("📦 发布文件位于 release/ 目录")
    print("=" * 50)
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
