# StudentFoto 证件照系统打包方案

## 📦 打包概述

本系统提供**两个独立的安装包**，分别用于不同的部署场景：

### 1. PC端安装包
- **用途**: 安装在拍照工作站
- **功能**: 连接手机拍照、连接服务端处理证件照
- **部署**: 每台拍照PC都需要安装

### 2. 服务端安装包  
- **用途**: 安装在服务器或主控PC
- **功能**: 身份证读卡、任务分配、证件照处理
- **部署**: 整个系统只需要一台服务端

## 🚀 快速打包

### 方法一：一键打包（推荐）
```bash
# 双击运行批处理文件
一键打包.bat
```

### 方法二：使用Python脚本
```bash
# 运行打包工具
python 打包工具.py
```

### 方法三：手动打包
```bash
# PC端
cd pc-client
python -m PyInstaller --onefile --windowed main.py

# 服务端
cd id_card_server
python -m PyInstaller --onefile --console main.py
```

## 🔧 详细打包步骤

### 环境准备
1. **Python环境**: Python 3.7+
2. **依赖包**: 
   ```bash
   pip install pyinstaller PyQt5 Pillow requests
   ```

### PC端打包
```bash
cd pc-client
python build_exe.py
```

**生成文件**:
- `dist/StudentFoto-PC客户端.exe` - 主程序
- `release/` - 发布包目录

### 服务端打包
```bash
cd id_card_server

# 构建C++桥接程序（可选）
build_bridge.bat

# 打包Python程序
python -m PyInstaller --onefile --console --name "StudentFoto-服务端" main.py
```

**生成文件**:
- `dist/StudentFoto-服务端.exe` - 主程序
- `id_card_bridge.exe` - 身份证读卡器桥接程序

## 📁 安装包结构

### PC端安装包内容
```
StudentFoto-PC客户端-v1.0.0-20241230_120000/
├── StudentFoto-PC客户端.exe     # 主程序
├── 启动.bat                     # 启动脚本
├── 使用说明.txt                 # 使用指南
└── version.json                 # 版本信息
```

### 服务端安装包内容
```
StudentFoto-服务端-v1.0.0-20241230_120000/
├── StudentFoto-服务端.exe       # 主程序
├── id_card_bridge.exe           # 读卡器桥接程序
├── photo_settings.json          # 证件照设置
├── 启动.bat                     # 启动脚本
├── 安装.bat                     # 安装脚本
├── 使用说明.txt                 # 使用指南
├── version.json                 # 版本信息
└── data/                        # 数据目录
```

## 🎯 打包选项说明

### 快速打包 (quick_build.py)
- **优点**: 速度快，文件小
- **缺点**: 功能简化
- **适用**: 开发测试、快速部署

### 完整打包 (build_packages.py)
- **优点**: 功能完整，包含安装脚本
- **缺点**: 速度慢，文件大
- **适用**: 正式发布、生产环境

## 📋 打包配置

### PyInstaller参数说明
```python
# PC端配置
--onefile          # 单文件模式
--windowed          # 无控制台窗口
--name "程序名"     # 指定程序名称
--add-data "源;目标" # 添加数据文件
--hidden-import     # 隐式导入模块

# 服务端配置  
--console           # 显示控制台窗口
--add-binary        # 添加二进制文件
```

### 自定义配置
修改 `build_packages.py` 中的配置：
```python
# 版本号
self.version = "1.0.0"

# 程序名称
name='StudentFoto-PC客户端'

# 添加文件
datas=[
    ('ui/*.py', 'ui'),
    ('config.py', '.'),
]
```

## 🔍 故障排除

### 常见问题

1. **PyInstaller未安装**
   ```bash
   pip install pyinstaller
   ```

2. **模块导入错误**
   - 添加 `--hidden-import` 参数
   - 检查依赖包是否安装

3. **文件路径错误**
   - 使用绝对路径
   - 检查 `--add-data` 参数格式

4. **C++编译失败**
   - 安装Visual Studio Build Tools
   - 或使用预编译的 `id_card_bridge.exe`

### 调试方法

1. **查看详细输出**
   ```bash
   python -m PyInstaller --debug=all main.py
   ```

2. **测试导入**
   ```python
   import PyQt5.QtCore
   import PIL.Image
   ```

3. **检查文件**
   ```bash
   # 查看生成的文件
   dir dist\
   ```

## 📦 分发建议

### 文件命名规范
```
StudentFoto-PC客户端-v{版本号}-{时间戳}.zip
StudentFoto-服务端-v{版本号}-{时间戳}.zip
```

### 版本管理
- 使用语义化版本号 (如: 1.0.0)
- 包含构建时间戳
- 记录版本变更日志

### 分发方式
1. **内网分发**: 网络共享文件夹
2. **U盘分发**: 复制到移动存储设备
3. **下载分发**: 上传到文件服务器

## 🚀 自动化打包

### 批处理脚本
```batch
@echo off
echo 开始自动打包...
python build_packages.py
echo 打包完成！
pause
```

### 定时任务
- 使用Windows任务计划程序
- 设置每日自动构建
- 生成夜间构建版本

## 📞 技术支持

### 打包相关问题
- 检查Python版本和依赖
- 查看PyInstaller日志
- 确认文件路径正确

### 部署相关问题  
- 检查目标系统兼容性
- 确认网络配置
- 验证权限设置

---

**注意**: 首次打包可能需要较长时间，后续打包会更快。建议在打包前测试程序功能正常。
