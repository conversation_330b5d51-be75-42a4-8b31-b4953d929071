# 服务端手动任务管理功能说明

## 功能概述

为服务端添加了手动操作任务的功能，包括重新分配指定任务和删除任务，提供了更灵活的任务管理能力。

## 新增功能

### 1. 手动重新分配任务
- **功能**：将已分配或暂停的任务重新设置为待分配状态
- **触发方式**：
  - 按钮操作：选中任务后点击"重新分配选中任务"按钮
  - 右键菜单：在任务表格中右键选择"重新分配任务"
- **支持批量操作**：可以同时选择多个任务进行重新分配

### 2. 删除任务
- **功能**：彻底删除选中的任务
- **触发方式**：
  - 按钮操作：选中任务后点击"删除选中任务"按钮
  - 右键菜单：在任务表格中右键选择"删除任务"
- **支持批量操作**：可以同时选择多个任务进行删除
- **安全确认**：删除前会显示确认对话框

### 3. 查看任务详情
- **功能**：显示任务的完整信息
- **触发方式**：右键菜单选择"查看任务详情"
- **显示内容**：任务ID、个人信息、状态、时间等完整信息

## 界面改进

### 任务列表增强
- **多选支持**：任务表格支持多行选择（Ctrl+点击或Shift+点击）
- **操作按钮**：在任务列表下方添加操作按钮
  - 🔄 重新分配选中任务（红色按钮）
  - 🗑️ 删除选中任务（灰色按钮）
- **右键菜单**：在任务行上右键显示上下文菜单
  - 重新分配任务
  - 删除任务
  - 查看任务详情

### 用户体验优化
- **操作确认**：重要操作前显示确认对话框
- **详细信息**：确认对话框中显示将要操作的任务列表
- **操作反馈**：操作完成后显示结果消息
- **日志记录**：所有手动操作都会记录到系统日志

## 技术实现

### 数据库层面
**新增方法：`reassign_task_to_pending()`**
```python
def reassign_task_to_pending(self, task_id: str) -> bool:
    """将指定任务重新设置为待分配状态"""
    # 将任务状态设置为pending，清除客户端分配和时间信息
```

**新增方法：`delete_task()`**
```python
def delete_task(self, task_id: str) -> bool:
    """删除指定任务"""
    # 从数据库中彻底删除任务记录
```

### UI层面
**任务表格增强**
- 启用多选模式：`setSelectionMode(QTableWidget.MultiSelection)`
- 启用右键菜单：`setContextMenuPolicy(Qt.CustomContextMenu)`
- 添加操作按钮布局

**新增方法**
- `reassign_selected_tasks()`：重新分配选中任务
- `delete_selected_tasks()`：删除选中任务
- `show_task_context_menu()`：显示右键菜单
- `show_task_detail()`：显示任务详情

## 工作流程

### 重新分配任务流程
1. 用户选择一个或多个任务
2. 点击"重新分配"按钮或右键菜单
3. 系统显示确认对话框，列出将要重新分配的任务
4. 用户确认后，系统执行以下操作：
   - 将任务状态设置为`pending`
   - 清除`pc_client_id`和`assigned_time`
   - 触发自动任务分配逻辑
   - 将任务重新分配给可用的PC客户端
5. 更新界面显示和统计信息
6. 记录操作日志

### 删除任务流程
1. 用户选择一个或多个任务
2. 点击"删除"按钮或右键菜单
3. 系统显示确认对话框，警告操作不可撤销
4. 用户确认后，系统从数据库中删除任务记录
5. 更新界面显示和统计信息
6. 记录操作日志

## 使用场景

### 1. 任务重新分配
- **PC客户端故障**：当某个PC客户端出现问题时，可以将其任务重新分配给其他客户端
- **负载调整**：手动调整任务分配，平衡各客户端的工作负载
- **任务卡住**：当任务长时间未处理时，可以重新分配给其他客户端

### 2. 任务清理
- **重复任务**：删除因系统错误产生的重复任务
- **无效任务**：删除身份证信息错误或无效的任务
- **测试数据**：清理测试过程中产生的测试任务

### 3. 任务监控
- **状态查看**：查看任务的详细状态和处理历史
- **问题排查**：通过任务详情排查处理问题
- **数据核对**：核对任务信息的准确性

## 安全特性

### 操作确认
- 所有重要操作都需要用户确认
- 确认对话框显示详细的操作信息
- 删除操作特别标注"不可撤销"

### 日志记录
- 所有手动操作都会记录到系统日志
- 包含操作类型、任务信息、操作时间
- 便于审计和问题追踪

### 数据完整性
- 重新分配任务会触发自动分配逻辑
- 确保任务不会丢失或重复分配
- 维护数据库的一致性

## 测试验证

**测试脚本：`test_manual_reassign.py`**
- ✅ 任务创建和分配
- ✅ 单个任务重新分配
- ✅ 批量任务重新分配
- ✅ 任务删除功能
- ✅ 状态变化验证

## 部署说明

1. **无需额外配置**：功能已集成到现有服务端界面
2. **向后兼容**：不影响现有功能和数据
3. **即时生效**：服务端重启后立即可用

## 操作指南

### 重新分配任务
1. 在任务列表中选择要重新分配的任务（可多选）
2. 点击"重新分配选中任务"按钮或右键选择"重新分配任务"
3. 在确认对话框中检查任务列表
4. 点击"是"确认操作
5. 系统会自动将任务重新分配给可用的PC客户端

### 删除任务
1. 在任务列表中选择要删除的任务（可多选）
2. 点击"删除选中任务"按钮或右键选择"删除任务"
3. 在确认对话框中检查任务列表，注意"不可撤销"警告
4. 点击"是"确认删除
5. 任务将从系统中彻底删除

### 查看任务详情
1. 在任务列表中选择一个任务
2. 右键选择"查看任务详情"
3. 在弹出的对话框中查看完整的任务信息

## 总结

手动任务管理功能为服务端提供了更强的任务控制能力，管理员可以根据实际情况灵活调整任务分配，处理异常情况，提高系统的可维护性和可靠性。
