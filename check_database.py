#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库任务状态
"""

import sys
import os
sys.path.append('.')

from id_card_server.core.database import DatabaseManager

def check_database():
    """检查数据库状态"""
    try:
        db = DatabaseManager()
        
        print('=== 数据库任务统计 ===')
        stats = db.get_task_statistics()
        print(f'总任务数: {stats["total"]}')
        print(f'待分配: {stats["pending"]}')
        print(f'已分配: {stats["assigned"]}')
        print(f'已完成: {stats["completed"]}')
        print(f'暂停: {stats["suspended"]}')
        
        print('\n=== 未分配任务列表 ===')
        unassigned = db.get_unassigned_tasks()
        print(f'未分配任务数量: {len(unassigned)}')
        for i, task in enumerate(unassigned[:5]):  # 只显示前5个
            print(f'{i+1}. 任务ID: {task["task_id"]}, 姓名: {task["name"]}, 状态: {task["task_status"]}')
        
        print('\n=== 所有任务状态 ===')
        all_tasks = db.get_all_tasks()
        print(f'所有任务数量: {len(all_tasks)}')
        for i, task in enumerate(all_tasks[:10]):  # 只显示前10个
            print(f'{i+1}. ID: {task[0]}, 姓名: {task[1]}, 状态: {task[5]}, PC端: {task[6]}')
            
        # 检查测试任务表
        print('\n=== 测试任务统计 ===')
        try:
            test_stats = db.get_task_statistics("test_tasks")
            print(f'测试任务总数: {test_stats["total"]}')
            print(f'测试待分配: {test_stats["pending"]}')
            print(f'测试已分配: {test_stats["assigned"]}')
            print(f'测试已完成: {test_stats["completed"]}')
            
            if test_stats["total"] > 0:
                print('\n=== 测试任务未分配列表 ===')
                test_unassigned = db.get_unassigned_tasks("test_tasks")
                print(f'测试未分配任务数量: {len(test_unassigned)}')
                for i, task in enumerate(test_unassigned[:5]):
                    print(f'{i+1}. 任务ID: {task["task_id"]}, 姓名: {task["name"]}, 状态: {task["task_status"]}')
        except Exception as e:
            print(f'测试任务表不存在或查询失败: {e}')
            
    except Exception as e:
        print(f'检查数据库失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_database()
