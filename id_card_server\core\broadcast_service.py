#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
广播服务
用于向网络中的PC客户端广播服务端信息
"""

import socket
import threading
import time
import json
import logging
from typing import Dict, Any
from PyQt5.QtCore import QObject, pyqtSignal
import config

logger = logging.getLogger(__name__)

class BroadcastService(QObject):
    """广播服务类"""
    
    # 信号定义
    broadcast_started = pyqtSignal()  # 广播开始
    broadcast_stopped = pyqtSignal()  # 广播停止
    log_message = pyqtSignal(str)     # 日志消息
    
    def __init__(self, server_port=None):
        super().__init__()
        self.server_port = server_port or config.SERVER_PORT
        self.broadcast_port = config.BROADCAST_PORT
        self.broadcast_interval = config.BROADCAST_INTERVAL
        self.running = False
        self.broadcast_thread = None
        self.socket = None
        
    def start_broadcast(self):
        """启动广播服务"""
        if self.running:
            return
            
        try:
            self.running = True
            self.broadcast_thread = threading.Thread(target=self._broadcast_loop, daemon=True)
            self.broadcast_thread.start()
            
            self.log_message.emit(f"广播服务已启动，端口: {self.broadcast_port}")
            self.broadcast_started.emit()
            logger.info(f"广播服务已启动，端口: {self.broadcast_port}")
            
        except Exception as e:
            self.running = False
            error_msg = f"启动广播服务失败: {e}"
            self.log_message.emit(error_msg)
            logger.error(error_msg)
    
    def stop_broadcast(self):
        """停止广播服务"""
        if not self.running:
            return
            
        self.running = False
        
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None
            
        if self.broadcast_thread and self.broadcast_thread.is_alive():
            self.broadcast_thread.join(timeout=2)
            
        self.log_message.emit("广播服务已停止")
        self.broadcast_stopped.emit()
        logger.info("广播服务已停止")
    
    def _broadcast_loop(self):
        """广播循环"""
        try:
            # 创建UDP套接字
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            
            # 获取本机IP地址
            server_ip = self._get_local_ip()
            
            # 构建广播消息
            broadcast_data = {
                "type": "server_discovery",
                "message": config.BROADCAST_MESSAGE,
                "server_ip": server_ip,
                "server_port": self.server_port,
                "server_name": config.APP_NAME,
                "version": config.APP_VERSION,
                "timestamp": int(time.time())
            }
            
            broadcast_message = json.dumps(broadcast_data).encode('utf-8')
            
            while self.running:
                try:
                    # 更新时间戳
                    broadcast_data["timestamp"] = int(time.time())
                    broadcast_message = json.dumps(broadcast_data).encode('utf-8')
                    
                    # 发送广播
                    self.socket.sendto(broadcast_message, ('<broadcast>', self.broadcast_port))
                    
                    # 等待下次广播
                    time.sleep(self.broadcast_interval)
                    
                except Exception as e:
                    if self.running:  # 只在运行状态下记录错误
                        logger.error(f"广播发送失败: {e}")
                        time.sleep(1)  # 短暂等待后重试
                        
        except Exception as e:
            error_msg = f"广播服务异常: {e}"
            self.log_message.emit(error_msg)
            logger.error(error_msg)
        finally:
            if self.socket:
                try:
                    self.socket.close()
                except:
                    pass
                self.socket = None
    
    def _get_local_ip(self) -> str:
        """获取本机IP地址"""
        try:
            # 连接到一个远程地址来获取本机IP
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                s.connect(("*******", 80))
                local_ip = s.getsockname()[0]
                return local_ip
        except Exception:
            try:
                # 备用方法：获取主机名对应的IP
                hostname = socket.gethostname()
                local_ip = socket.gethostbyname(hostname)
                return local_ip
            except Exception:
                # 最后的备用方法
                return "127.0.0.1"
    
    def get_broadcast_info(self) -> Dict[str, Any]:
        """获取广播信息"""
        return {
            "running": self.running,
            "server_port": self.server_port,
            "broadcast_port": self.broadcast_port,
            "broadcast_interval": self.broadcast_interval,
            "server_ip": self._get_local_ip()
        }
    
    def update_server_port(self, port: int):
        """更新服务端口"""
        self.server_port = port
        if self.running:
            # 重启广播服务以使用新端口
            self.stop_broadcast()
            time.sleep(0.5)
            self.start_broadcast()
