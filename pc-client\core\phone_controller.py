#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手机控制器
负责与Android应用通信
"""

import socket
import threading
import time
import os
import tempfile
import json
from PyQt5.QtCore import QObject, pyqtSignal

class PhoneController(QObject):
    """手机控制器类"""
    
    # 信号定义
    connected = pyqtSignal()
    disconnected = pyqtSignal()
    photo_taken = pyqtSignal(str)  # 照片路径
    error_occurred = pyqtSignal(str)  # 错误信息
    log_message = pyqtSignal(str)  # 日志信息
    photo_confirmed = pyqtSignal()  # 照片确认
    photo_rejected = pyqtSignal()   # 照片拒绝
    ready_to_receive = pyqtSignal() # 手机准备接收照片
    
    def __init__(self):
        super().__init__()
        self.socket = None
        self.is_connected_flag = False
        self.phone_ip = ""
        self.phone_port = 8080
        self.photos_dir = os.path.join(tempfile.gettempdir(), "student_photos")
        self._current_photo_uri = None
        self._photo_buffer = bytearray()
        self._expecting_photo_data = False
        self._expected_photo_size = 0
        self._last_progress = -1
        self._unexpected_data_warned = False

        # 配置文件路径
        self.config_file = "photo_config.json"

        # 加载照片配置
        self._load_photo_config()

        # 创建照片存储目录结构
        self._create_photo_directories()
            
    def is_connected(self):
        """检查是否已连接"""
        return self.is_connected_flag
        
    def connect_to_phone(self, ip, port):
        """连接到手机"""
        self.phone_ip = ip
        self.phone_port = port
        
        # 在新线程中执行连接
        thread = threading.Thread(target=self._connect_thread)
        thread.daemon = True
        thread.start()
        
    def _connect_thread(self):
        """连接线程"""
        try:
            self.log_message.emit(f"正在连接到 {self.phone_ip}:{self.phone_port}...")
            
            # 创建socket连接
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(10)  # 10秒超时
            
            # 连接到手机
            self.socket.connect((self.phone_ip, self.phone_port))
            
            self.is_connected_flag = True
            self.log_message.emit("连接成功!")
            self.connected.emit()

            # 启动接收线程
            receive_thread = threading.Thread(target=self._receive_thread)
            receive_thread.daemon = True
            receive_thread.start()
            
        except socket.timeout:
            self.error_occurred.emit("连接超时，请检查手机IP地址和网络连接")
        except ConnectionRefusedError:
            self.error_occurred.emit("连接被拒绝，请确保手机应用正在运行")
        except Exception as e:
            self.error_occurred.emit(f"连接失败: {str(e)}")
            
    def _receive_thread(self):
        """接收数据线程"""
        try:
            # 设置socket超时，避免阻塞
            self.socket.settimeout(1.0)

            while self.is_connected_flag and self.socket:
                try:
                    # 接收数据
                    data = self.socket.recv(1024)
                    if not data:
                        self.log_message.emit("手机端断开连接")
                        break

                    # 尝试解码为文本消息
                    try:
                        message = data.decode('utf-8').strip()
                        self.log_message.emit(f"收到消息: {message}")

                        # 处理不同类型的消息
                        if message == "CONNECTED":
                            self.log_message.emit("连接确认成功")
                        elif message == "COMMAND_RECEIVED":
                            self.log_message.emit("手机已收到拍照命令")
                        elif message.startswith("PHOTO_TAKEN:"):
                            # 新格式：PHOTO_TAKEN:uri
                            uri = message[12:]  # 去掉"PHOTO_TAKEN:"前缀
                            self.log_message.emit(f"手机拍照完成，照片URI: {uri}")
                            # 准备接收照片数据
                            self._current_photo_uri = uri
                        elif message == "TASK_RECEIVED":
                            self.log_message.emit("手机已收到任务信息")
                        elif message == "READY_FOR_PHOTO":
                            self.log_message.emit("手机准备接收处理后照片")
                        elif message == "READY_TO_RECEIVE":
                            self.log_message.emit("手机确认准备接收照片")
                            self.ready_to_receive.emit()
                        elif message == "PHOTO_CONFIRMED":
                            self.log_message.emit("手机确认照片")
                            self.photo_confirmed.emit()
                        elif message == "PHOTO_REJECTED":
                            self.log_message.emit("手机拒绝照片，需要重新拍照")
                            self.photo_rejected.emit()
                        elif message.startswith("PHOTO_DATA:"):
                            # 照片数据头：PHOTO_DATA:size
                            size_str = message[11:]  # 去掉"PHOTO_DATA:"前缀
                            try:
                                photo_size = int(size_str)
                                self.log_message.emit(f"准备接收照片数据: {photo_size} 字节")
                                self._expecting_photo_data = True
                                self._expected_photo_size = photo_size
                                self._photo_buffer = bytearray()
                            except ValueError:
                                self.log_message.emit(f"无效的照片大小: {size_str}")
                        elif message == "PHOTO_TAKEN":
                            # 兼容旧格式
                            self._handle_photo_taken()
                        elif message == "PONG":
                            self.log_message.emit("心跳响应正常")
                        elif message == "PHOTO_END":
                            # 照片传输结束标记（文本消息形式）
                            if self._expecting_photo_data:
                                self.log_message.emit("收到照片结束标记（文本消息），正在保存...")
                                self._expecting_photo_data = False
                                self._save_received_photo()
                            # 如果不在等待照片数据状态，直接忽略（可能已经在二进制数据处理中处理过了）
                        elif message.startswith("ERROR:"):
                            error_msg = message[6:]  # 去掉"ERROR:"前缀
                            self.error_occurred.emit(f"手机端错误: {error_msg}")
                        else:
                            self.log_message.emit(f"未知消息: {message}")

                    except UnicodeDecodeError:
                        # 这是二进制数据，可能是照片数据的一部分
                        # 首先尝试在数据中查找PHOTO_DATA消息
                        if b"PHOTO_DATA:" in data:
                            # 数据中包含PHOTO_DATA头信息
                            try:
                                text_part = data.decode('utf-8', errors='ignore')
                                if "PHOTO_DATA:" in text_part:
                                    lines = text_part.split('\n')
                                    for line in lines:
                                        if line.startswith("PHOTO_DATA:"):
                                            size_str = line[11:]  # 去掉"PHOTO_DATA:"前缀
                                            try:
                                                photo_size = int(size_str)
                                                self.log_message.emit(f"从混合数据中解析到照片大小: {photo_size} 字节")
                                                self._expecting_photo_data = True
                                                self._expected_photo_size = photo_size
                                                self._photo_buffer = bytearray()
                                                break
                                            except ValueError:
                                                pass
                            except:
                                pass

                        # 如果我们正在等待照片数据，将其添加到缓冲区
                        if self._expecting_photo_data:
                            # 检查数据中是否包含PHOTO_END标记
                            if b"PHOTO_END\n" in data:
                                # 找到结束标记，分离照片数据和结束标记
                                end_marker_pos = data.find(b"PHOTO_END\n")
                                photo_data_part = data[:end_marker_pos]
                                self._photo_buffer.extend(photo_data_part)

                                self.log_message.emit("收到照片结束标记（二进制数据），正在保存...")
                                self._expecting_photo_data = False
                                self._save_received_photo()
                            else:
                                # 普通的照片数据
                                self._photo_buffer.extend(data)
                                progress = (len(self._photo_buffer) / self._expected_photo_size) * 100

                                # 只每10%显示一次进度，避免日志爆表
                                if int(progress) % 10 == 0 and int(progress) != getattr(self, '_last_progress', -1):
                                    self.log_message.emit(f"照片接收进度: {progress:.0f}%")
                                    self._last_progress = int(progress)

                                # 检查是否接收完成（备用检查，主要依靠PHOTO_END标记）
                                if len(self._photo_buffer) >= self._expected_photo_size:
                                    self.log_message.emit("照片数据大小已达到预期，等待结束标记...")
                        else:
                            # 检查是否包含PHOTO_DATA消息（可能混在二进制数据中）
                            if b"PHOTO_DATA:" in data:
                                try:
                                    # 尝试提取PHOTO_DATA信息
                                    data_str = data.decode('utf-8', errors='ignore')
                                    lines = data_str.split('\n')
                                    for line in lines:
                                        if line.startswith("PHOTO_DATA:"):
                                            size_str = line[11:]
                                            try:
                                                photo_size = int(size_str)
                                                self.log_message.emit(f"从混合数据中解析到照片大小: {photo_size} 字节")
                                                self._expecting_photo_data = True
                                                self._expected_photo_size = photo_size
                                                self._photo_buffer = bytearray()

                                                # 检查是否有照片数据在同一个包中
                                                photo_data_start = data.find(b"PHOTO_DATA:")
                                                if photo_data_start >= 0:
                                                    # 找到换行符后的数据
                                                    newline_pos = data.find(b'\n', photo_data_start)
                                                    if newline_pos >= 0:
                                                        remaining_data = data[newline_pos + 1:]
                                                        if remaining_data:
                                                            self._photo_buffer.extend(remaining_data)
                                                            self.log_message.emit(f"同包中包含照片数据: {len(remaining_data)} 字节")
                                                break
                                            except ValueError:
                                                pass
                                except:
                                    pass

                            # 如果仍然没有期待照片数据，显示警告
                            if not self._expecting_photo_data and not getattr(self, '_unexpected_data_warned', False):
                                self.log_message.emit("收到意外的二进制数据，可能是照片数据但未收到大小信息")
                                self._unexpected_data_warned = True

                except socket.timeout:
                    # 超时是正常的，继续循环
                    continue
                except ConnectionResetError:
                    self.log_message.emit("连接被手机端重置")
                    break
                except Exception as e:
                    if self.is_connected_flag:
                        self.log_message.emit(f"接收数据时出错: {str(e)}")
                    break

        except Exception as e:
            if self.is_connected_flag:
                self.error_occurred.emit(f"接收线程错误: {str(e)}")
        finally:
            self._cleanup_connection()



    def _save_received_photo(self):
        """保存接收到的照片到raw目录"""
        try:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            photo_filename = f"photo_{timestamp}.jpg"

            # 保存到raw目录
            raw_dir = self.get_raw_photos_directory()
            photo_path = os.path.join(raw_dir, photo_filename)

            # 保存照片数据
            with open(photo_path, 'wb') as f:
                f.write(self._photo_buffer)

            self.log_message.emit(f"原始照片已保存: {photo_path}")
            self.photo_taken.emit(photo_path)

            # 清理缓冲区
            self._photo_buffer = bytearray()
            self._current_photo_uri = None

        except Exception as e:
            self.error_occurred.emit(f"保存照片失败: {str(e)}")

    def _handle_photo_taken(self, uri=None):
        """处理拍照完成（仅用于兼容旧格式或测试）"""
        self.log_message.emit("收到旧格式拍照通知，创建模拟照片")
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        photo_filename = f"photo_{timestamp}_dummy.jpg"

        # 保存到raw目录
        raw_dir = self.get_raw_photos_directory()
        photo_path = os.path.join(raw_dir, photo_filename)

        self._create_dummy_photo(photo_path, uri)
        self.photo_taken.emit(photo_path)
        
    def _create_dummy_photo(self, photo_path, uri=None):
        """创建模拟照片（用于测试）"""
        try:
            from PIL import Image, ImageDraw, ImageFont

            # 创建一个800x600的图像
            img = Image.new('RGB', (800, 600))
            img.paste((173, 216, 230), (0, 0, 800, 600))  # lightblue background
            draw = ImageDraw.Draw(img)

            # 添加文本
            timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
            text = f"学生拍照系统\n拍摄时间: {timestamp}\n手机IP: {self.phone_ip}"
            if uri:
                text += f"\n照片URI: {uri}"

            try:
                # 尝试使用系统字体
                font = ImageFont.truetype("arial.ttf", 20)
            except:
                # 如果没有找到字体，使用默认字体
                font = ImageFont.load_default()

            # 计算文本位置
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            x = (800 - text_width) // 2
            y = (600 - text_height) // 2

            # 绘制文本
            draw.text((x, y), text, fill='black', font=font)

            # 保存图像
            img.save(photo_path, 'JPEG')

        except ImportError:
            # 如果没有PIL库，创建一个空文件
            with open(photo_path, 'w') as f:
                content = f"模拟照片文件\n拍摄时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n手机IP: {self.phone_ip}"
                if uri:
                    content += f"\n照片URI: {uri}"
                f.write(content)
        except Exception as e:
            self.log_message.emit(f"创建模拟照片失败: {str(e)}")
            
    def take_photo(self):
        """发送拍照命令"""
        if not self.is_connected_flag or not self.socket:
            self.error_occurred.emit("未连接到手机")
            return
            
        try:
            # 发送拍照命令
            command = "TAKE_PHOTO\n"
            self.socket.send(command.encode('utf-8'))
            self.log_message.emit("已发送拍照命令")
            
        except Exception as e:
            self.error_occurred.emit(f"发送拍照命令失败: {str(e)}")
            
    def send_ping(self):
        """发送心跳包"""
        if not self.is_connected_flag or not self.socket:
            return False

        try:
            self.socket.send("PING\n".encode('utf-8'))
            return True
        except Exception as e:
            self.log_message.emit(f"发送心跳失败: {str(e)}")
            return False

    def send_task_info(self, task_info):
        """发送任务信息到手机"""
        if not self.is_connected_flag or not self.socket:
            self.error_occurred.emit("未连接到手机")
            return False

        try:
            # 构建任务信息JSON
            task_json = json.dumps(task_info, ensure_ascii=False)
            command = f"TASK_INFO:{task_json}\n"
            self.socket.send(command.encode('utf-8'))
            self.log_message.emit("已发送任务信息到手机")
            return True

        except Exception as e:
            self.error_occurred.emit(f"发送任务信息失败: {str(e)}")
            return False

    def send_pc_action(self, action):
        """发送PC端操作到手机端（用于状态同步）"""
        if not self.is_connected_flag or not self.socket:
            self.error_occurred.emit("未连接到手机")
            return False

        try:
            command = f"{action}\n"
            self.socket.send(command.encode('utf-8'))
            self.log_message.emit(f"已发送PC端操作到手机: {action}")
            return True

        except Exception as e:
            self.error_occurred.emit(f"发送PC端操作失败: {str(e)}")
            return False

    def send_processing_complete_notification(self):
        """发送处理完成通知到手机"""
        if not self.is_connected_flag or not self.socket:
            self.error_occurred.emit("未连接到手机")
            return False

        try:
            message = "PROCESSING_COMPLETE\n"
            self.socket.send(message.encode('utf-8'))
            return True
        except Exception as e:
            self.error_occurred.emit(f"发送处理完成通知失败: {str(e)}")
            return False

    def send_photo_data_directly(self, photo_path):
        """直接发送照片数据到手机（在确认机制中使用）"""
        if not self.is_connected_flag or not self.socket:
            self.error_occurred.emit("未连接到手机")
            return False

        try:
            # 读取照片文件
            with open(photo_path, 'rb') as f:
                photo_data = f.read()

            self.log_message.emit(f"直接发送处理后照片数据: {len(photo_data)} 字节")

            # 发送照片大小信息
            size_command = f"PROCESSED_PHOTO:{len(photo_data)}\n"
            self.socket.send(size_command.encode('utf-8'))

            # 等待手机确认准备接收
            time.sleep(0.1)

            # 发送照片数据
            self.socket.send(photo_data)

            # 发送结束标记
            time.sleep(0.1)
            self.socket.send("\nPHOTO_END\n".encode('utf-8'))

            self.log_message.emit("处理后照片数据发送完成")
            return True

        except Exception as e:
            self.error_occurred.emit(f"直接发送照片数据失败: {str(e)}")
            return False

    def send_processed_photo(self, photo_path):
        """发送处理后的照片到手机"""
        if not self.is_connected_flag or not self.socket:
            self.error_occurred.emit("未连接到手机")
            return False

        try:
            # 读取照片文件
            with open(photo_path, 'rb') as f:
                photo_data = f.read()

            self.log_message.emit(f"准备发送处理后照片: {len(photo_data)} 字节")

            # 发送照片大小信息
            size_command = f"PROCESSED_PHOTO:{len(photo_data)}\n"
            self.socket.send(size_command.encode('utf-8'))

            # 等待手机确认准备接收
            # 这里简化处理，实际应该等待READY_FOR_PHOTO响应
            time.sleep(0.1)

            # 发送照片数据
            self.socket.send(photo_data)

            # 发送结束标记
            time.sleep(0.1)
            self.socket.send("\nPHOTO_END\n".encode('utf-8'))

            self.log_message.emit("处理后照片发送完成")
            return True

        except Exception as e:
            self.error_occurred.emit(f"发送处理后照片失败: {str(e)}")
            return False

    def disconnect(self):
        """断开连接"""
        if not self.is_connected_flag:
            self.log_message.emit("已经处于断开状态")
            return

        self.log_message.emit("正在断开连接...")

        # 发送断开连接命令
        if self.socket:
            try:
                self.socket.send("DISCONNECT".encode('utf-8'))
                time.sleep(0.1)  # 给手机端一点时间处理
            except:
                pass

        # 设置断开标志并清理连接
        was_connected = self.is_connected_flag
        self.is_connected_flag = False
        self._cleanup_connection(was_connected)

    def _cleanup_connection(self, emit_disconnected=None):
        """清理连接"""
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None

        # 如果明确指定要发射断开信号，或者当前仍处于连接状态
        if emit_disconnected or (emit_disconnected is None and self.is_connected_flag):
            self.is_connected_flag = False
            self.log_message.emit("连接已断开")
            self.disconnected.emit()

        # 重置照片接收状态
        self._expecting_photo_data = False
        self._expected_photo_size = 0
        self._photo_buffer = bytearray()
        self._last_progress = -1
        self._unexpected_data_warned = False
            
    def get_photos_directory(self):
        """获取照片存储根目录"""
        return self.photos_dir

    def get_raw_photos_directory(self):
        """获取原始照片存储目录"""
        return os.path.join(self.photos_dir, "raw")

    def get_id_photos_directory(self):
        """获取证件照存储目录"""
        return os.path.join(self.photos_dir, "id")

    def _create_photo_directories(self):
        """创建照片存储目录结构"""
        try:
            # 创建根目录
            if not os.path.exists(self.photos_dir):
                os.makedirs(self.photos_dir)
                self.log_message.emit(f"创建照片根目录: {self.photos_dir}")

            # 创建raw子目录
            raw_dir = self.get_raw_photos_directory()
            if not os.path.exists(raw_dir):
                os.makedirs(raw_dir)
                self.log_message.emit(f"创建原始照片目录: {raw_dir}")

            # 创建id子目录
            id_dir = self.get_id_photos_directory()
            if not os.path.exists(id_dir):
                os.makedirs(id_dir)
                self.log_message.emit(f"创建证件照目录: {id_dir}")

        except Exception as e:
            self.log_message.emit(f"创建目录结构失败: {e}")

    def _load_photo_config(self):
        """加载照片配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 加载照片目录配置
                photos_config = config.get('photos', {})
                custom_photos_dir = photos_config.get('directory', '')
                if custom_photos_dir and os.path.exists(os.path.dirname(custom_photos_dir)):
                    old_dir = self.photos_dir
                    self.photos_dir = custom_photos_dir
                    self.log_message.emit(f"已加载照片目录配置: {self.photos_dir}")

                    # 如果目录发生了变化，需要重新创建目录结构
                    if old_dir != self.photos_dir:
                        self.log_message.emit(f"照片目录已变更，重新创建目录结构")

        except Exception as e:
            self.log_message.emit(f"加载照片配置失败: {e}")

    def _save_photo_config(self):
        """保存照片配置文件"""
        try:
            config = {}
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

            # 获取照片统计信息
            stats = self.get_photo_stats()

            config['photos'] = {
                'directory': self.photos_dir,
                'total_photos': stats['total_photos'],
                'raw_photos': stats['raw_photos'],
                'id_photos': stats['id_photos'],
                'last_updated': time.strftime('%Y-%m-%d %H:%M:%S')
            }

            config['app'] = {
                'version': '1.0',
                'last_updated': time.strftime('%Y-%m-%d %H:%M:%S')
            }

            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            self.log_message.emit("照片配置已保存")

        except Exception as e:
            self.log_message.emit(f"保存照片配置失败: {e}")

    def set_photos_directory(self, directory):
        """设置照片存储目录"""
        if os.path.exists(directory) or os.path.exists(os.path.dirname(directory)):
            old_dir = self.photos_dir
            self.photos_dir = directory

            # 创建新的目录结构
            self._create_photo_directories()

            self._save_photo_config()
            self.log_message.emit(f"照片目录已更改: {old_dir} -> {self.photos_dir}")
            return True
        else:
            self.log_message.emit(f"无效的目录路径: {directory}")
            return False

    def get_photo_stats(self):
        """获取照片统计信息"""
        try:
            raw_dir = self.get_raw_photos_directory()
            id_dir = self.get_id_photos_directory()

            # 统计原始照片
            raw_photos = []
            if os.path.exists(raw_dir):
                raw_photos = [f for f in os.listdir(raw_dir)
                            if f.lower().endswith(('.jpg', '.jpeg', '.png'))]

            # 统计证件照
            id_photos = []
            if os.path.exists(id_dir):
                id_photos = [f for f in os.listdir(id_dir)
                           if f.lower().endswith(('.jpg', '.jpeg', '.png'))]

            return {
                'total_photos': len(raw_photos) + len(id_photos),
                'raw_photos': len(raw_photos),
                'id_photos': len(id_photos),
                'directory': self.photos_dir,
                'raw_directory': raw_dir,
                'id_directory': id_dir,
                'recent_raw_photos': sorted(raw_photos, reverse=True)[:5] if raw_photos else [],
                'recent_id_photos': sorted(id_photos, reverse=True)[:5] if id_photos else []
            }
        except Exception as e:
            self.log_message.emit(f"获取照片统计失败: {e}")
            return {
                'total_photos': 0, 'raw_photos': 0, 'id_photos': 0,
                'directory': self.photos_dir, 'raw_directory': '', 'id_directory': '',
                'recent_raw_photos': [], 'recent_id_photos': []
            }
