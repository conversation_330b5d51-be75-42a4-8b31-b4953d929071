# 身份证读卡器桥接程序说明

## 问题背景

由于华视电子身份证阅读器SDK提供的是32位DLL文件，而我们的Python环境是64位的，直接加载会出现架构不匹配错误：
```
[WinError 193] %1 不是有效的 Win32 应用程序
```

## 解决方案

使用32位C++程序作为桥接，通过Socket通信与64位Python程序交互：

```
64位Python程序 ←→ Socket通信 ←→ 32位C++桥接程序 ←→ 32位DLL
```

## 文件说明

### 1. id_card_bridge.cpp
32位C++桥接程序源代码，功能包括：
- 加载华视SDK的32位DLL
- 提供Socket服务器监听Python请求
- 实现读卡器连接、断开、读卡等功能
- 返回JSON格式的响应

### 2. build_bridge.bat
编译脚本，自动检测Visual Studio环境并编译32位程序：
- 自动查找Visual Studio安装路径
- 设置32位编译环境
- 编译生成 id_card_bridge.exe

### 3. CMakeLists.txt
CMake构建文件（可选），用于更复杂的构建需求

## 编译步骤

### 方法1：使用批处理脚本（推荐）
```bash
# 双击运行或在命令行执行
build_bridge.bat
```

### 方法2：手动编译
1. 打开"Developer Command Prompt for VS"（32位）
2. 进入项目目录
3. 执行编译命令：
```bash
cl /EHsc /Fe:id_card_bridge.exe id_card_bridge.cpp ws2_32.lib
```

### 方法3：使用CMake
```bash
mkdir build
cd build
cmake .. -A Win32
cmake --build . --config Release
```

## 使用流程

1. **编译桥接程序**
   ```bash
   build_bridge.bat
   ```

2. **运行Python程序**
   ```bash
   python main.py
   ```

3. **自动流程**
   - Python检测到32位DLL + 64位Python的架构不匹配
   - 自动启动32位桥接程序 (id_card_bridge.exe)
   - 通过Socket通信实现读卡功能

## 通信协议

### 连接读卡器
**请求**:
```json
{"command": "connect", "port": 1001}
```

**响应**:
```json
{"success": true}
```

### 读取身份证
**请求**:
```json
{"command": "read_card"}
```

**响应**:
```json
{
    "success": true,
    "data": {
        "name": "张三",
        "gender": "男",
        "birth_date": "19900101",
        "id_number": "123456789012345678"
    }
}
```

### 断开连接
**请求**:
```json
{"command": "disconnect"}
```

**响应**:
```json
{"success": true}
```

## 错误处理

### 编译错误
1. **未找到Visual Studio**
   - 安装Visual Studio Community（免费）
   - 确保安装了C++开发工具

2. **编译失败**
   - 检查源代码语法
   - 确保使用32位编译环境

### 运行错误
1. **桥接程序不存在**
   - 运行 build_bridge.bat 编译程序
   - 确保生成了 id_card_bridge.exe

2. **连接失败**
   - 检查端口9091是否被占用
   - 确保防火墙允许程序通信

3. **DLL加载失败**
   - 确保server目录下有完整的SDK文件
   - 检查DLL文件是否损坏

## 优势

✅ **架构兼容**: 32位桥接程序可以正常加载32位DLL  
✅ **透明使用**: Python代码无需大幅修改  
✅ **稳定可靠**: C++程序性能稳定，资源占用少  
✅ **易于维护**: 桥接程序逻辑简单，便于调试  

## 注意事项

1. **编译环境**: 必须使用32位编译环境
2. **DLL依赖**: 确保所有SDK文件完整
3. **端口占用**: 默认使用9091端口，如有冲突可修改
4. **进程管理**: Python程序退出时会自动清理桥接进程

---

这个解决方案完美解决了64位Python调用32位DLL的架构不匹配问题，让程序能够正常使用华视电子身份证阅读器。
