#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
照片参数设置对话框
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QLabel, QPushButton, QComboBox, QSpinBox,
                             QDoubleSpinBox, QGroupBox, QColorDialog, QFrame,
                             QCheckBox, QTabWidget, QWidget)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QColor
import config

class PhotoSettingsDialog(QDialog):
    """照片参数设置对话框"""
    
    def __init__(self, current_settings, parent=None):
        super().__init__(parent)
        self.current_settings = current_settings.copy()
        self.init_ui()
        self.load_settings()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("照片参数设置")
        self.setFixedSize(500, 700)

        layout = QVBoxLayout(self)

        # 创建选项卡
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)

        # 基本设置选项卡
        basic_tab = QWidget()
        tab_widget.addTab(basic_tab, "基本设置")
        self.init_basic_tab(basic_tab)

        # 高级设置选项卡
        advanced_tab = QWidget()
        tab_widget.addTab(advanced_tab, "高级设置")
        self.init_advanced_tab(advanced_tab)

        # 图像调整选项卡
        image_tab = QWidget()
        tab_widget.addTab(image_tab, "图像调整")
        self.init_image_tab(image_tab)

        # 按钮
        button_layout = QHBoxLayout()

        self.ok_btn = QPushButton("确定")
        self.ok_btn.clicked.connect(self.accept)
        button_layout.addWidget(self.ok_btn)

        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)

        layout.addLayout(button_layout)

    def init_basic_tab(self, tab):
        """初始化基本设置选项卡"""
        layout = QVBoxLayout(tab)

        # 照片尺寸设置
        size_group = QGroupBox("照片尺寸")
        size_layout = QFormLayout(size_group)

        self.size_combo = QComboBox()
        for size in config.PHOTO_SIZES:
            self.size_combo.addItem(f"{size['name']} ({size['description']})")
        self.size_combo.addItem("自定义")
        self.size_combo.currentTextChanged.connect(self.on_size_changed)
        size_layout.addRow("照片规格:", self.size_combo)

        self.width_spin = QSpinBox()
        self.width_spin.setRange(100, 2000)
        self.width_spin.setSuffix(" px")
        size_layout.addRow("宽度:", self.width_spin)

        self.height_spin = QSpinBox()
        self.height_spin.setRange(100, 2000)
        self.height_spin.setSuffix(" px")
        size_layout.addRow("高度:", self.height_spin)

        self.dpi_spin = QSpinBox()
        self.dpi_spin.setRange(72, 600)
        self.dpi_spin.setValue(300)
        size_layout.addRow("DPI:", self.dpi_spin)

        layout.addWidget(size_group)
        
        # 背景设置
        bg_group = QGroupBox("背景设置")
        bg_layout = QFormLayout(bg_group)

        self.bg_color_combo = QComboBox()
        for color in config.BACKGROUND_COLORS:
            self.bg_color_combo.addItem(f"{color['name']} ({color['hex_value']})")
        self.bg_color_combo.addItem("自定义")
        self.bg_color_combo.currentTextChanged.connect(self.on_bg_color_changed)
        bg_layout.addRow("背景颜色:", self.bg_color_combo)

        # 自定义颜色选择
        color_layout = QHBoxLayout()
        self.color_frame = QFrame()
        self.color_frame.setFixedSize(50, 30)
        self.color_frame.setStyleSheet("border: 1px solid black;")
        color_layout.addWidget(self.color_frame)

        self.color_btn = QPushButton("选择颜色")
        self.color_btn.clicked.connect(self.select_custom_color)
        color_layout.addWidget(self.color_btn)
        color_layout.addStretch()

        bg_layout.addRow("自定义颜色:", color_layout)

        # 渲染模式
        self.render_mode_combo = QComboBox()
        self.render_mode_combo.addItems(["纯色", "上下渐变", "中心渐变"])
        bg_layout.addRow("渲染模式:", self.render_mode_combo)

        layout.addWidget(bg_group)

        # 模型设置
        model_group = QGroupBox("AI模型设置")
        model_layout = QFormLayout(model_group)

        self.matting_model_combo = QComboBox()
        for model in config.MATTING_MODELS:
            self.matting_model_combo.addItem(f"{model['name']} - {model['description']}")
        model_layout.addRow("抠图模型:", self.matting_model_combo)

        self.face_detect_combo = QComboBox()
        for model in config.FACE_DETECT_MODELS:
            self.face_detect_combo.addItem(f"{model['name']} - {model['description']}")
        model_layout.addRow("人脸检测:", self.face_detect_combo)

        layout.addWidget(model_group)

    def init_advanced_tab(self, tab):
        """初始化高级设置选项卡"""
        layout = QVBoxLayout(tab)

        # 质量设置
        quality_group = QGroupBox("质量设置")
        quality_layout = QFormLayout(quality_group)

        # 移除高清模式选项，默认只生成标准照片
        self.face_alignment_check = QCheckBox("人脸对齐")
        quality_layout.addRow("", self.face_alignment_check)

        layout.addWidget(quality_group)
        
        # 人脸检测参数
        face_group = QGroupBox("人脸检测参数")
        face_layout = QFormLayout(face_group)

        self.head_measure_spin = QDoubleSpinBox()
        self.head_measure_spin.setRange(0.1, 1.0)
        self.head_measure_spin.setSingleStep(0.05)
        self.head_measure_spin.setDecimals(2)
        face_layout.addRow("头部测量比例:", self.head_measure_spin)

        self.head_height_spin = QDoubleSpinBox()
        self.head_height_spin.setRange(0.1, 1.0)
        self.head_height_spin.setSingleStep(0.05)
        self.head_height_spin.setDecimals(2)
        face_layout.addRow("头部高度比例:", self.head_height_spin)

        self.top_distance_max_spin = QDoubleSpinBox()
        self.top_distance_max_spin.setRange(0.05, 0.5)
        self.top_distance_max_spin.setSingleStep(0.01)
        self.top_distance_max_spin.setDecimals(2)
        face_layout.addRow("顶部距离最大值:", self.top_distance_max_spin)

        self.top_distance_min_spin = QDoubleSpinBox()
        self.top_distance_min_spin.setRange(0.05, 0.5)
        self.top_distance_min_spin.setSingleStep(0.01)
        self.top_distance_min_spin.setDecimals(2)
        face_layout.addRow("顶部距离最小值:", self.top_distance_min_spin)

        layout.addWidget(face_group)

        # 输出设置
        output_group = QGroupBox("输出设置")
        output_layout = QFormLayout(output_group)

        self.kb_size_spin = QSpinBox()
        self.kb_size_spin.setRange(0, 2048)
        self.kb_size_spin.setValue(0)
        self.kb_size_spin.setSuffix(" KB (0=不限制)")
        output_layout.addRow("文件大小限制:", self.kb_size_spin)

        layout.addWidget(output_group)

    def init_image_tab(self, tab):
        """初始化图像调整选项卡"""
        layout = QVBoxLayout(tab)

        # 图像调整
        adjust_group = QGroupBox("图像调整")
        adjust_layout = QFormLayout(adjust_group)

        self.brightness_spin = QDoubleSpinBox()
        self.brightness_spin.setRange(-1.0, 1.0)
        self.brightness_spin.setSingleStep(0.1)
        self.brightness_spin.setDecimals(1)
        adjust_layout.addRow("亮度调整:", self.brightness_spin)

        self.contrast_spin = QDoubleSpinBox()
        self.contrast_spin.setRange(-1.0, 1.0)
        self.contrast_spin.setSingleStep(0.1)
        self.contrast_spin.setDecimals(1)
        adjust_layout.addRow("对比度调整:", self.contrast_spin)

        self.sharpen_spin = QDoubleSpinBox()
        self.sharpen_spin.setRange(0.0, 2.0)
        self.sharpen_spin.setSingleStep(0.1)
        self.sharpen_spin.setDecimals(1)
        adjust_layout.addRow("锐化强度:", self.sharpen_spin)

        self.saturation_spin = QDoubleSpinBox()
        self.saturation_spin.setRange(-1.0, 1.0)
        self.saturation_spin.setSingleStep(0.1)
        self.saturation_spin.setDecimals(1)
        adjust_layout.addRow("饱和度调整:", self.saturation_spin)

        layout.addWidget(adjust_group)
        layout.addStretch()
        
    def load_settings(self):
        """加载当前设置"""
        settings = self.current_settings

        # 照片尺寸
        size_info = settings.get("size", {})
        size_name = size_info.get("name", "一寸")

        # 查找匹配的尺寸
        for i, size in enumerate(config.PHOTO_SIZES):
            if size["name"] == size_name:
                self.size_combo.setCurrentIndex(i)
                break
        else:
            self.size_combo.setCurrentIndex(len(config.PHOTO_SIZES))  # 自定义

        self.width_spin.setValue(size_info.get("width", 295))
        self.height_spin.setValue(size_info.get("height", 413))
        self.dpi_spin.setValue(settings.get("dpi", 300))

        # 背景颜色
        bg_info = settings.get("background_color", {})
        bg_name = bg_info.get("name", "蓝色")

        # 查找匹配的背景色
        for i, color in enumerate(config.BACKGROUND_COLORS):
            if color["name"] == bg_name:
                self.bg_color_combo.setCurrentIndex(i)
                break
        else:
            self.bg_color_combo.setCurrentIndex(len(config.BACKGROUND_COLORS))  # 自定义

        # 设置自定义颜色显示
        hex_value = bg_info.get("hex_value", "#438EDB")
        color = QColor(hex_value)
        self.color_frame.setStyleSheet(f"background-color: {color.name()}; border: 1px solid black;")

        # 渲染模式
        self.render_mode_combo.setCurrentIndex(settings.get("render_mode", 0))

        # 模型设置
        matting_info = settings.get("matting_model", {})
        matting_name = matting_info.get("name", "modnet_photographic_portrait_matting")
        for i, model in enumerate(config.MATTING_MODELS):
            if model["name"] == matting_name:
                self.matting_model_combo.setCurrentIndex(i)
                break

        face_info = settings.get("face_detect_model", {})
        face_name = face_info.get("name", "mtcnn")
        for i, model in enumerate(config.FACE_DETECT_MODELS):
            if model["name"] == face_name:
                self.face_detect_combo.setCurrentIndex(i)
                break

        # 高级设置
        self.face_alignment_check.setChecked(settings.get("face_alignment", True))

        # 人脸检测参数
        self.head_measure_spin.setValue(settings.get("head_measure_ratio", 0.2))
        self.head_height_spin.setValue(settings.get("head_height_ratio", 0.45))
        self.top_distance_max_spin.setValue(settings.get("top_distance_max", 0.12))
        self.top_distance_min_spin.setValue(settings.get("top_distance_min", 0.10))

        # 输出设置
        kb_size = settings.get("kb_size")
        self.kb_size_spin.setValue(kb_size if kb_size else 0)

        # 图像调整
        self.brightness_spin.setValue(settings.get("brightness_strength", 0))
        self.contrast_spin.setValue(settings.get("contrast_strength", 0))
        self.sharpen_spin.setValue(settings.get("sharpen_strength", 0))
        self.saturation_spin.setValue(settings.get("saturation_strength", 0))
        
    def on_size_changed(self, size_text):
        """照片规格改变"""
        # 从显示文本中提取尺寸名称
        size_name = size_text.split(" (")[0]

        # 查找对应的尺寸配置
        for size in config.PHOTO_SIZES:
            if size["name"] == size_name:
                self.width_spin.setValue(size["width"])
                self.height_spin.setValue(size["height"])
                break

    def on_bg_color_changed(self, color_text):
        """背景颜色改变"""
        # 从显示文本中提取颜色名称
        color_name = color_text.split(" (")[0]

        # 查找对应的颜色配置
        for color in config.BACKGROUND_COLORS:
            if color["name"] == color_name:
                hex_value = color["hex_value"]
                qcolor = QColor(hex_value)
                self.color_frame.setStyleSheet(f"background-color: {qcolor.name()}; border: 1px solid black;")
                break
            
    def select_custom_color(self):
        """选择自定义颜色"""
        color = QColorDialog.getColor(Qt.blue, self)
        if color.isValid():
            self.color_frame.setStyleSheet(f"background-color: {color.name()}; border: 1px solid black;")
            self.bg_color_combo.setCurrentText("自定义")
            
    def get_settings(self):
        """获取设置"""
        # 获取尺寸设置
        size_text = self.size_combo.currentText()
        size_name = size_text.split(" (")[0]

        if size_name == "自定义":
            size_info = {
                "name": "自定义",
                "width": self.width_spin.value(),
                "height": self.height_spin.value(),
                "description": f"{self.width_spin.value()}×{self.height_spin.value()}"
            }
        else:
            # 查找预定义尺寸
            size_info = None
            for size in config.PHOTO_SIZES:
                if size["name"] == size_name:
                    size_info = size.copy()
                    size_info["width"] = self.width_spin.value()
                    size_info["height"] = self.height_spin.value()
                    break
            if not size_info:
                size_info = {"name": size_name, "width": self.width_spin.value(), "height": self.height_spin.value()}

        # 获取背景色设置
        bg_text = self.bg_color_combo.currentText()
        bg_name = bg_text.split(" (")[0]

        if bg_name == "自定义":
            # 从color_frame获取颜色
            style = self.color_frame.styleSheet()
            hex_value = style.split("background-color: ")[1].split(";")[0]
            bg_info = {
                "name": "自定义",
                "hex_value": hex_value,
                "description": "自定义颜色"
            }
        else:
            # 查找预定义颜色
            bg_info = None
            for color in config.BACKGROUND_COLORS:
                if color["name"] == bg_name:
                    bg_info = color.copy()
                    break
            if not bg_info:
                bg_info = {"name": bg_name, "hex_value": "#438EDB"}

        # 获取模型设置
        matting_index = self.matting_model_combo.currentIndex()
        matting_info = config.MATTING_MODELS[matting_index] if matting_index < len(config.MATTING_MODELS) else config.MATTING_MODELS[0]

        face_index = self.face_detect_combo.currentIndex()
        face_info = config.FACE_DETECT_MODELS[face_index] if face_index < len(config.FACE_DETECT_MODELS) else config.FACE_DETECT_MODELS[0]

        return {
            # 基本设置
            "size": size_info,
            "background_color": bg_info,
            "matting_model": matting_info,
            "face_detect_model": face_info,

            # 高级设置
            "dpi": self.dpi_spin.value(),
            "hd": False,  # 固定为False，不生成高清照片
            "face_alignment": self.face_alignment_check.isChecked(),
            "head_measure_ratio": self.head_measure_spin.value(),
            "head_height_ratio": self.head_height_spin.value(),
            "top_distance_max": self.top_distance_max_spin.value(),
            "top_distance_min": self.top_distance_min_spin.value(),

            # 图像调整
            "brightness_strength": self.brightness_spin.value(),
            "contrast_strength": self.contrast_spin.value(),
            "sharpen_strength": self.sharpen_spin.value(),
            "saturation_strength": self.saturation_spin.value(),

            # 输出设置
            "kb_size": self.kb_size_spin.value() if self.kb_size_spin.value() > 0 else None,
            "render_mode": self.render_mode_combo.currentIndex()
        }
