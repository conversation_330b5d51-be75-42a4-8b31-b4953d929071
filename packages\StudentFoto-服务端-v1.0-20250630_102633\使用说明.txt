# StudentFoto 服务端 v1.0

## 🚀 使用方法
1. 双击 StudentFoto-服务端.exe 启动程序
2. 查看程序显示的IP地址和端口号
3. 等待PC端连接并分配机位号
4. 连接身份证读卡器开始工作

## 💻 系统要求
- Windows 7 或更高版本
- 至少4GB可用内存
- 身份证读卡器及驱动（可选）
- 网络连接

## 🔧 功能说明
- 身份证读卡器集成
- PC端连接管理
- 任务分配和调度
- 证件照处理
- 数据库存储

## 📁 文件说明
- StudentFoto-服务端.exe: 主程序
- id_card_bridge.exe: 身份证读卡器桥接程序（32位）
- photo_settings.json: 证件照处理设置
- server/: 身份证读卡器SDK文件夹
  - *.dll: 华视电子身份证读卡器SDK动态库
  - license.dat: SDK授权文件
  - 身份证阅读器SDK使用手册.md: SDK使用说明

## 🔧 故障排除
- 首次运行会自动创建数据库
- 确保防火墙允许程序访问网络
- 检查身份证读卡器连接和驱动
- 查看程序日志排查问题

## 📋 网络配置
- 默认端口: 9090
- 确保PC端能访问此端口
- 记录IP地址供PC端连接使用

构建时间: 2025-06-30 10:26:34
