# 学生拍照系统 - PC端客户端

这是一个用于远程控制Android手机拍照的PC端客户端程序，使用Python和PyQt5开发。

## 功能特性

- 🔗 **远程连接**: 通过WiFi连接到Android手机应用
- 📸 **远程拍照**: 一键控制手机拍照
- 🖼️ **照片预览**: 实时查看拍摄的照片，支持自动旋转校正
- 💾 **照片保存**: 将照片保存到本地指定目录
- 📝 **操作日志**: 详细的操作记录和状态显示
- 🔄 **自动重连**: 连接断开时自动尝试重连
- 📱 **设备状态**: 显示手机设备方向和连接状态

## 系统要求

- Python 3.7+
- PyQt5
- Pillow (用于图像处理)
- Windows/macOS/Linux

## 安装步骤

1. **克隆项目**
   ```bash
   git clone <项目地址>
   cd pc-client
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **运行程序**
   ```bash
   python run.py
   ```
   或者
   ```bash
   python main.py
   ```

## 使用说明

### 1. 连接手机

1. 确保电脑和手机连接在同一个WiFi网络
2. 在手机上启动"学生拍照"应用
3. 在手机应用中查看显示的IP地址
4. 在电脑客户端中输入手机的IP地址
5. 点击"连接手机"按钮

### 2. 拍照操作

- **单次拍照**: 点击"📸 拍照"按钮
- 照片会自动根据手机方向进行旋转校正
- 拍照完成后照片会自动显示在右侧预览区域

### 3. 照片管理

- **查看照片**: 在右侧照片查看器中点击照片列表查看大图
- **保存照片**: 选择照片后点击"💾 保存照片"按钮，照片会保存到当前目录
- **删除照片**: 选择照片后点击"🗑️ 删除照片"按钮
- **清空列表**: 点击"🧹 清空列表"按钮清除所有照片
- **自动命名**: 照片自动按时间戳命名，格式为 `photo_YYYYMMDD_HHMMSS.jpg`

## 界面说明

### 左侧控制面板
- **连接设置**: 输入手机IP地址和端口
- **拍照控制**: 拍照和连续拍照按钮
- **状态信息**: 显示连接状态和进度
- **操作日志**: 显示详细的操作记录

### 右侧照片查看器
- **照片列表**: 显示所有拍摄的照片缩略图
- **照片预览**: 显示选中照片的大图
- **照片信息**: 显示照片的详细信息
- **操作按钮**: 保存、删除、清空等操作

## 网络配置

默认端口: `8080`

确保防火墙允许该端口的通信。

## 故障排除

### 连接问题
1. **连接超时**
   - 检查手机和电脑是否在同一WiFi网络
   - 确认手机应用正在运行
   - 检查IP地址是否正确

2. **连接被拒绝**
   - 确保手机应用已启动并监听端口
   - 检查防火墙设置
   - 尝试重启手机应用

### 拍照问题
1. **拍照失败**
   - 检查手机相机权限是否已授予
   - 确认手机应用在前台运行
   - 检查手机存储空间是否充足
   - 查看错误日志获取详细信息

2. **照片无法显示**
   - 检查网络连接是否稳定
   - 确认照片传输完成
   - 重新拍摄照片

3. **照片方向错误**
   - 应用会自动根据手机方向校正照片
   - 如果仍有问题，请检查手机重力感应器是否正常

## 开发说明

### 项目结构
```
pc-client/
├── main.py              # 主程序入口
├── run.py               # 启动脚本
├── requirements.txt     # 依赖包列表
├── README.md           # 说明文档
├── ui/                 # 用户界面模块
│   ├── __init__.py
│   ├── main_window.py  # 主窗口
│   └── photo_viewer.py # 照片查看器
└── core/               # 核心功能模块
    ├── __init__.py
    └── phone_controller.py # 手机控制器
```

### 通信协议

#### PC端发送命令
- **拍照命令**: `TAKE_PHOTO`
- **心跳检测**: `PING`
- **断开连接**: `DISCONNECT`

#### 手机端响应
- **连接确认**: `CONNECTED`
- **命令确认**: `COMMAND_RECEIVED`
- **拍照完成**: `PHOTO_TAKEN:<uri>`
- **照片数据**: `PHOTO_DATA:<size>` + 二进制数据 + `PHOTO_END`
- **心跳响应**: `PONG`
- **错误信息**: `ERROR:<错误描述>`

#### 数据传输格式
1. 文本命令使用UTF-8编码
2. 照片数据为JPEG格式的二进制流
3. 每个命令/响应以换行符结束（除二进制数据外）

## 许可证

本项目采用 MIT 许可证。

## 联系方式

如有问题或建议，请联系开发团队。
