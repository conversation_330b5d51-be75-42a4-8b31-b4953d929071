#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务管理器
"""

import socket
import threading
import json
import base64
import uuid
import logging
import time
import os
from datetime import datetime
from typing import Dict, Any
from PyQt5.QtCore import QObject, pyqtSignal
import config

logger = logging.getLogger(__name__)

class TaskManager(QObject):
    """任务管理器"""
    
    # 信号定义
    client_connected = pyqtSignal(str, str)  # client_id, address
    client_disconnected = pyqtSignal(str)    # client_id
    task_assigned = pyqtSignal(str, str)     # task_id, client_id
    photo_uploaded = pyqtSignal(str, str)    # task_id, client_id
    task_cancelled = pyqtSignal(str)         # task_id
    log_message = pyqtSignal(str)            # log message
    
    def __init__(self, database_manager, port=9090):
        super().__init__()
        self.db = database_manager
        self.port = port
        self.server_socket = None
        self.running = False
        self.connected_clients = {}  # 存储连接的PC端信息
        self.photo_settings = config.DEFAULT_PHOTO_SETTINGS.copy()  # 照片参数设置
        self.client_stations = {}  # 存储客户端机位号分配 {client_id: station_number}
        self.machine_stations = {}  # 存储机器码到机位号的映射 {machine_id: station_number}
        self.next_station_number = 1  # 下一个可分配的机位号
        self.task_source = "photo_tasks"  # 任务来源表名，默认为正式任务

        # 加载保存的照片设置
        self.load_photo_settings()

        # 加载机位号分配记录
        self.load_station_assignments()

    def set_task_source(self, table_name: str):
        """设置任务来源表"""
        if table_name in ["photo_tasks", "test_tasks"]:
            self.task_source = table_name
            logger.info(f"任务来源已设置为: {table_name}")
        else:
            logger.warning(f"无效的任务来源表名: {table_name}")

    def start_server(self):
        """启动服务器"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind(('0.0.0.0', self.port))
            self.server_socket.listen(5)
            self.running = True
            
            self.log_message.emit(f"任务服务器启动，监听端口: {self.port}")
            logger.info(f"任务服务器启动，监听端口: {self.port}")
            
            while self.running:
                try:
                    client_socket, address = self.server_socket.accept()
                    
                    # 为每个客户端生成唯一ID
                    client_id = str(uuid.uuid4())[:8]

                    address_str = f"{address[0]}:{address[1]}"
                    self.log_message.emit(f"PC客户端连接: {address_str} (ID: {client_id})")
                    logger.info(f"新客户端连接: {address_str} (ID: {client_id})")
                    self.client_connected.emit(client_id, address_str)
                    
                    client_thread = threading.Thread(
                        target=self.handle_client,
                        args=(client_socket, address, client_id)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                    
                except Exception as e:
                    if self.running:
                        logger.error(f"接受连接时出错: {e}")
                        self.log_message.emit(f"接受连接时出错: {e}")
                        
        except Exception as e:
            logger.error(f"启动服务器失败: {e}")
            self.log_message.emit(f"启动服务器失败: {e}")
    
    def stop_server(self):
        """停止服务器"""
        self.running = False
        
        # 关闭所有客户端连接
        for client_id, info in list(self.connected_clients.items()):
            try:
                info["socket"].close()
            except:
                pass
        
        self.connected_clients.clear()
        
        # 关闭服务器socket
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass
        
        self.log_message.emit("任务服务器已停止")
        logger.info("任务服务器已停止")
    
    def handle_client(self, client_socket, address, client_id):
        """处理客户端连接"""
        try:
            # 存储客户端连接信息（先存储基本信息）
            current_time = time.time()
            self.connected_clients[client_id] = {
                "socket": client_socket,
                "address": address,
                "connected_time": current_time,
                "last_heartbeat": current_time,
                "machine_id": None,
                "machine_info": None,
                "station_number": 1  # 临时分配，等待连接消息更新
            }

            address_str = f"{address[0]}:{address[1]}"
            self.log_message.emit(f"客户端连接: {address_str} (ID: {client_id})")
            self.client_connected.emit(client_id, address_str)

            while self.running:
                # 接收消息，支持大消息
                data = self._receive_large_message(client_socket)
                if not data:
                    break

                try:
                    message = json.loads(data)
                    logger.info(f"收到客户端 {client_id} 消息: {message.get('type', 'unknown')}")

                    response = self.process_message(message, client_id)

                    # 发送响应
                    try:
                        response_str = json.dumps(response)
                        client_socket.send(response_str.encode('utf-8'))
                        logger.info(f"已向客户端 {client_id} 发送响应: {response.get('status', 'unknown')}")
                    except Exception as send_error:
                        logger.error(f"向客户端 {client_id} 发送响应失败: {send_error}")
                        break  # 发送失败，断开连接

                except json.JSONDecodeError as e:
                    logger.error(f"JSON解析错误: {e}, 数据长度: {len(data) if data else 0}")
                    try:
                        error_response = {"status": "error", "message": f"Invalid JSON: {str(e)}"}
                        client_socket.send(json.dumps(error_response).encode('utf-8'))
                    except:
                        logger.error(f"发送错误响应失败")
                        break
                except Exception as process_error:
                    logger.error(f"处理消息异常: {process_error}")
                    try:
                        error_response = {"status": "error", "message": f"Server error: {str(process_error)}"}
                        client_socket.send(json.dumps(error_response).encode('utf-8'))
                    except:
                        logger.error(f"发送错误响应失败")
                        break
                    
        except Exception as e:
            logger.error(f"处理客户端 {address} 时出错: {e}")
        finally:
            # 处理客户端断开连接
            self._handle_client_disconnect(client_id, address)

            try:
                client_socket.close()
            except:
                pass

    def _handle_client_disconnect(self, client_id: str, address: tuple):
        """处理客户端断开连接"""
        try:
            # 获取客户端信息
            client_info = self.connected_clients.get(client_id)
            station_number = client_info.get("station_number", 0) if client_info else 0

            # 将该客户端的未完成任务重置为未分配状态，供其他PC端接收
            reset_count = self.db.reset_client_tasks_to_pending(client_id)
            if reset_count > 0:
                logger.info(f"客户端 {client_id} (机位{station_number}号) 断开连接，重置了 {reset_count} 个任务为未分配状态")
                self.log_message.emit(f"客户端断开连接，重置了 {reset_count} 个任务为未分配状态，可供其他PC端接收")

                # 重新分配任务给其他连接的PC端
                try:
                    self.check_and_assign_tasks()
                except Exception as e:
                    logger.error(f"重新分配任务异常: {e}")

            # 清理客户端连接信息（但保留机位号分配）
            if client_id in self.connected_clients:
                del self.connected_clients[client_id]

            # 注意：不清理机位号分配，保留给重连时使用
            # if client_id in self.client_stations:
            #     del self.client_stations[client_id]

            address_str = f"{address[0]}:{address[1]}"
            self.log_message.emit(f"客户端断开连接: {address_str} (ID: {client_id}, 机位{station_number}号)")
            self.client_disconnected.emit(client_id)

        except Exception as e:
            logger.error(f"处理客户端断开连接异常: {e}")

    def _receive_large_message(self, client_socket):
        """接收大消息（支持照片上传等大数据）"""
        try:
            # 先接收一小块数据
            try:
                data = client_socket.recv(8192).decode('utf-8')
                if not data:
                    logger.info("客户端关闭连接")
                    return None

                logger.debug(f"接收到数据，长度: {len(data)}")
            except Exception as recv_error:
                logger.error(f"接收数据失败: {recv_error}")
                return None

            # 如果数据看起来被截断了（通常以不完整的JSON结尾），继续接收
            while data and not self._is_complete_json(data):
                try:
                    client_socket.settimeout(1.0)  # 设置短超时
                    more_data = client_socket.recv(8192).decode('utf-8')
                    if more_data:
                        data += more_data
                        logger.debug(f"继续接收数据，总长度: {len(data)}")
                    else:
                        break
                except socket.timeout:
                    # 超时说明数据接收完毕
                    logger.debug("接收超时，数据接收完毕")
                    break
                except Exception as more_error:
                    logger.error(f"继续接收数据失败: {more_error}")
                    break
                finally:
                    try:
                        client_socket.settimeout(None)  # 恢复阻塞模式
                    except:
                        pass

            logger.debug(f"消息接收完成，总长度: {len(data) if data else 0}")
            return data

        except Exception as e:
            logger.error(f"接收大消息失败: {e}")
            import traceback
            logger.error(f"接收异常详情: {traceback.format_exc()}")
            return None

    def _is_complete_json(self, data):
        """检查是否是完整的JSON"""
        try:
            json.loads(data)
            return True
        except json.JSONDecodeError:
            return False

    def process_message(self, message: Dict[str, Any], client_id: str) -> Dict[str, Any]:
        """处理消息"""
        msg_type = message.get('type')
        
        try:
            if msg_type == 'connect':
                return self.handle_client_connect(message, client_id)
            elif msg_type == 'get_photo_settings':
                return self.get_photo_settings()
            elif msg_type == 'get_task':
                return self.get_pending_task(client_id)
            elif msg_type == 'upload_photo':
                return self.handle_photo_upload(message, client_id)
            elif msg_type == 'cancel_task':
                return self.cancel_task(message)
            elif msg_type == 'get_client_info':
                return self.get_client_info(client_id)
            elif msg_type == 'heartbeat':
                return self.handle_heartbeat(message, client_id)
            elif msg_type == 'ready_for_tasks':
                return self.handle_ready_for_tasks(message, client_id)
            else:
                return {"status": "error", "message": "Unknown message type"}
                
        except Exception as e:
            logger.error(f"处理消息异常: {e}")
            return {"status": "error", "message": f"Server error: {str(e)}"}

    def handle_client_connect(self, message: Dict[str, Any], client_id: str) -> Dict[str, Any]:
        """处理客户端连接"""
        try:
            client_info = message.get('client_info', {})
            machine_id = client_info.get('machine_id', '')
            machine_info = client_info.get('machine_info', {})

            # 默认机位号
            station_number = 1

            # 更新客户端信息
            if client_id in self.connected_clients:
                self.connected_clients[client_id]['info'] = client_info
                self.connected_clients[client_id]['machine_id'] = machine_id
                self.connected_clients[client_id]['machine_info'] = machine_info

                # 根据机器码分配机位号
                station_number = self.assign_station_by_machine_id(machine_id, client_id)
                self.connected_clients[client_id]['station_number'] = station_number

                # 更新日志显示
                address_str = f"{self.connected_clients[client_id]['address'][0]}:{self.connected_clients[client_id]['address'][1]}"
                logger.info(f"客户端连接完成: {address_str} (ID: {client_id}, 机器码: {machine_id}, 机位: {station_number}号)")

                # 检查是否有该客户端的暂停任务需要恢复
                resumed_tasks = self.db.resume_client_tasks(client_id)
                if resumed_tasks:
                    logger.info(f"客户端 {client_id} 重连，恢复了 {len(resumed_tasks)} 个暂停任务")
                    self.log_message.emit(f"客户端重连，恢复了 {len(resumed_tasks)} 个暂停任务")

                # 获取该客户端的所有未完成任务（包括刚恢复的和已分配的）
                incomplete_tasks = self.db.get_pc_client_incomplete_tasks(client_id)
                if incomplete_tasks:
                    logger.info(f"客户端 {client_id} 有 {len(incomplete_tasks)} 个未完成任务，等待客户端准备就绪后发送")
                    self.log_message.emit(f"重连客户端有 {len(incomplete_tasks)} 个未完成任务，等待准备就绪")
                    # 暂存未完成任务，等待客户端发送ready_for_tasks消息
                    self.connected_clients[client_id]['pending_incomplete_tasks'] = incomplete_tasks
                else:
                    self.connected_clients[client_id]['pending_incomplete_tasks'] = []

                # 标记客户端尚未准备接收任务
                self.connected_clients[client_id]['ready_for_tasks'] = False
                logger.info(f"客户端 {client_id} 连接完成，等待准备接收任务的信号")
            else:
                logger.warning(f"客户端 {client_id} 不在连接列表中，使用默认机位号")

            # 返回连接成功和照片参数
            return {
                "status": "success",
                "message": "Connected to server",
                "photo_settings": self.photo_settings,
                "server_mode": True,
                "station_number": station_number
            }
        except Exception as e:
            logger.error(f"处理客户端连接异常: {e}")
            import traceback
            logger.error(f"连接异常详情: {traceback.format_exc()}")
            return {"status": "error", "message": f"Connect failed: {str(e)}"}

    def handle_heartbeat(self, message: Dict[str, Any], client_id: str) -> Dict[str, Any]:
        """处理心跳包"""
        try:
            # 更新客户端最后活跃时间
            if client_id in self.connected_clients:
                self.connected_clients[client_id]['last_heartbeat'] = time.time()

            return {
                "status": "success",
                "message": "Heartbeat received",
                "server_time": time.time()
            }
        except Exception as e:
            logger.error(f"处理心跳包异常: {e}")
            return {"status": "error", "message": f"Heartbeat failed: {str(e)}"}

    def handle_ready_for_tasks(self, message: Dict[str, Any], client_id: str) -> Dict[str, Any]:
        """处理客户端准备接收任务的消息"""
        try:
            logger.info(f"收到客户端 {client_id} 的ready_for_tasks消息")
            logger.info(f"客户端 {client_id} 已准备接收任务")

            # 标记客户端已准备接收任务
            if client_id in self.connected_clients:
                self.connected_clients[client_id]['ready_for_tasks'] = True

                # 获取暂存的未完成任务数量（用于响应）
                pending_tasks = self.connected_clients[client_id].get('pending_incomplete_tasks', [])
                pending_count = len(pending_tasks)

                # 先返回响应，然后异步发送任务
                response = {
                    "status": "success",
                    "message": "Ready for tasks acknowledged",
                    "tasks_sent": pending_count
                }

                # 使用QTimer延迟发送任务，确保响应先发送
                # 直接处理任务分配，不使用QTimer（避免线程问题）
                try:
                    if pending_tasks:
                        logger.info(f"向客户端 {client_id} 发送 {len(pending_tasks)} 个未完成任务")
                        self.log_message.emit(f"向客户端发送 {len(pending_tasks)} 个未完成任务")
                        for task in pending_tasks:
                            self._send_task_to_client(client_id, task)
                        # 清空暂存任务
                        self.connected_clients[client_id]['pending_incomplete_tasks'] = []

                    # 检查并分配新任务
                    logger.info("ready_for_tasks处理完成，开始检查任务分配")
                    self.check_and_assign_tasks()
                except Exception as e:
                    logger.error(f"任务分配异常: {e}")

                return response
            else:
                logger.warning(f"客户端 {client_id} 不在连接列表中")
                return {"status": "error", "message": "Client not found"}

        except Exception as e:
            logger.error(f"处理准备接收任务消息异常: {e}")
            return {"status": "error", "message": f"Ready for tasks failed: {str(e)}"}

    def get_photo_settings(self) -> Dict[str, Any]:
        """获取照片参数设置"""
        return {
            "status": "success",
            "photo_settings": self.photo_settings
        }

    def update_photo_settings(self, settings: Dict[str, Any]) -> bool:
        """更新照片参数设置"""
        try:
            self.photo_settings.update(settings)
            logger.info(f"照片参数已更新: {settings}")

            # 保存设置到文件
            self.save_photo_settings()

            # 向所有连接的客户端广播新的照片参数
            self.broadcast_photo_settings_to_clients()

            return True
        except Exception as e:
            logger.error(f"更新照片参数失败: {e}")
            return False

    def broadcast_photo_settings_to_clients(self):
        """向所有连接的客户端广播照片参数"""
        try:
            settings_message = {
                "type": "photo_settings_updated",
                "photo_settings": self.photo_settings
            }

            # 向所有连接的客户端发送更新消息
            disconnected_clients = []
            for client_id, client_info in self.connected_clients.items():
                try:
                    client_socket = client_info.get('socket')
                    if client_socket:
                        client_socket.send(json.dumps(settings_message).encode('utf-8'))
                        logger.info(f"已向客户端 {client_id} 发送照片参数更新")
                except Exception as e:
                    logger.error(f"向客户端 {client_id} 发送照片参数更新失败: {e}")
                    disconnected_clients.append(client_id)

            # 清理断开连接的客户端
            for client_id in disconnected_clients:
                if client_id in self.connected_clients:
                    del self.connected_clients[client_id]

        except Exception as e:
            logger.error(f"广播照片参数失败: {e}")

    def get_pending_task(self, client_id: str) -> Dict[str, Any]:
        """获取待处理任务"""
        try:
            task = self.db.get_pending_task(client_id, self.task_source)

            if task:
                self.task_assigned.emit(task["task_id"], client_id)
                return {
                    "status": "success",
                    "task": task,
                    "client_id": client_id
                }
            else:
                return {"status": "no_task", "message": "No pending tasks"}

        except Exception as e:
            logger.error(f"获取任务失败: {e}")
            return {"status": "error", "message": f"Get task failed: {str(e)}"}

    def assign_task_to_client(self, client_id: str):
        """主动分配任务给客户端"""
        try:
            # 检查客户端是否还在连接状态
            if client_id not in self.connected_clients:
                logger.warning(f"客户端 {client_id} 已断开连接，跳过任务分配")
                return False

            task = self.db.get_pending_task(client_id, self.task_source)

            if task:
                client_socket = self.connected_clients[client_id].get('socket')
                if client_socket:
                    try:
                        # 发送任务分配消息
                        task_message = {
                            "type": "task_assigned",
                            "task": task
                        }

                        client_socket.send(json.dumps(task_message).encode('utf-8'))
                        self.task_assigned.emit(task["task_id"], client_id)
                        logger.info(f"已向客户端 {client_id} (机位{self.get_client_station_number(client_id)}号) 分配任务: {task['task_id']}")
                        return True
                    except Exception as send_error:
                        logger.error(f"向客户端 {client_id} 发送任务失败: {send_error}")
                        # 客户端可能已断开，从连接列表中移除
                        if client_id in self.connected_clients:
                            del self.connected_clients[client_id]
                        return False

            return False

        except Exception as e:
            logger.error(f"分配任务给客户端失败: {e}")
            return False

    def notify_task_deleted(self, task_id: str, client_id: str):
        """通知PC客户端任务已被删除"""
        try:
            client_socket = self.connected_clients.get(client_id, {}).get('socket')
            if client_socket:
                # 发送任务删除通知
                delete_message = {
                    "type": "task_deleted",
                    "task_id": task_id
                }
                client_socket.send(json.dumps(delete_message).encode('utf-8'))
                logger.info(f"已通知客户端 {client_id} 删除任务: {task_id}")
                return True
        except Exception as e:
            logger.error(f"通知客户端 {client_id} 删除任务失败: {e}")
            # 客户端可能已断开，从连接列表中移除
            if client_id in self.connected_clients:
                del self.connected_clients[client_id]
        return False

    def check_and_assign_tasks(self):
        """检查并分配任务给所有连接的客户端（负载均衡，每个机位最多5个未完成任务）"""
        try:
            logger.info(f"开始检查任务分配，任务来源: {self.task_source}")

            # 获取所有未分配的任务
            unassigned_tasks = self.db.get_unassigned_tasks(self.task_source)
            logger.info(f"找到 {len(unassigned_tasks)} 个未分配任务")

            if not unassigned_tasks:
                logger.info("没有未分配的任务")
                return

            # 获取所有连接且已准备接收任务的客户端
            ready_clients = []
            for client_id, client_info in self.connected_clients.items():
                if client_info.get('ready_for_tasks', False):
                    ready_clients.append(client_id)

            logger.info(f"找到 {len(ready_clients)} 个准备好的客户端: {ready_clients}")

            if not ready_clients:
                logger.warning("没有准备好接收任务的客户端，任务进入等待队列")
                return

            # 获取每个准备好的客户端的未完成任务数量（包括assigned和suspended状态）
            client_task_counts = {}
            for client_id in ready_clients:
                assigned_count = self.db.get_pc_client_incomplete_tasks_count(client_id)
                suspended_count = self.db.get_pc_client_suspended_tasks_count(client_id)
                total_count = assigned_count + suspended_count
                client_task_counts[client_id] = total_count

            # 为每个任务找到合适的客户端（负载最少且未超过5个任务的客户端）
            for task in unassigned_tasks:
                # 找到任务数量最少且未超过5个任务的客户端
                best_client = None
                min_tasks = float('inf')

                for client_id in ready_clients:
                    task_count = client_task_counts.get(client_id, 0)
                    if task_count < 5 and task_count < min_tasks:  # 限制最多5个未完成任务
                        best_client = client_id
                        min_tasks = task_count

                if best_client:
                    # 将任务分配给选定的客户端
                    if self.db.assign_task_to_client(task["task_id"], best_client):
                        # 发送任务分配消息给客户端
                        self._send_task_to_client(best_client, task)
                        client_task_counts[best_client] += 1  # 更新计数
                        logger.info(f"任务 {task['task_id']} 已分配给客户端 {best_client} (机位{self.get_client_station_number(best_client)}号)")
                else:
                    logger.warning(f"所有准备好的客户端都已达到最大任务数量(5个)，任务 {task['task_id']} 等待分配")
                    break  # 如果所有准备好的客户端都满了，停止分配

        except Exception as e:
            logger.error(f"检查分配任务异常: {e}")

    def _send_task_to_client(self, client_id: str, task: Dict[str, Any]):
        """发送任务给客户端"""
        try:
            client_socket = self.connected_clients.get(client_id, {}).get('socket')
            if client_socket:
                # 发送任务分配消息
                task_message = {
                    "type": "task_assigned",
                    "task": task
                }
                client_socket.send(json.dumps(task_message).encode('utf-8'))
                self.task_assigned.emit(task["task_id"], client_id)
                return True
        except Exception as e:
            logger.error(f"向客户端 {client_id} 发送任务失败: {e}")
            # 客户端可能已断开，从连接列表中移除
            if client_id in self.connected_clients:
                del self.connected_clients[client_id]
        return False

    def assign_next_task_to_client(self, client_id: str):
        """为指定客户端分配下一个任务（考虑任务数量限制）"""
        try:
            # 检查客户端是否还在连接状态
            if client_id not in self.connected_clients:
                logger.warning(f"客户端 {client_id} 已断开连接，无法分配任务")
                return False

            # 检查客户端当前的未完成任务数量
            current_task_count = self.db.get_pc_client_incomplete_tasks_count(client_id)
            if current_task_count >= 5:
                logger.warning(f"客户端 {client_id} 已有 {current_task_count} 个未完成任务，达到上限，无法分配新任务")
                return False

            # 检查是否有未分配的任务
            unassigned_tasks = self.db.get_unassigned_tasks(self.task_source)

            if unassigned_tasks:
                # 取第一个未分配的任务
                task = unassigned_tasks[0]

                # 将任务分配给客户端
                if self.db.assign_task_to_client(task["task_id"], client_id):
                    self.assign_task_to_client(client_id)
                    logger.info(f"为客户端 {client_id} 分配新任务: {task['task_id']} (当前未完成任务: {current_task_count + 1}/5)")
                    return True

            return False

        except Exception as e:
            logger.error(f"为客户端分配下一个任务失败: {e}")
            return False

    def handle_photo_upload(self, message: Dict[str, Any], client_id: str) -> Dict[str, Any]:
        """处理证件照上传"""
        try:
            task_id = message.get('task_id')
            photo_data_b64 = message.get('photo_data')  # base64编码的证件照
            photo_filename = message.get('photo_filename')
            
            if not all([task_id, photo_data_b64, photo_filename]):
                return {"status": "error", "message": "Missing required fields"}
            
            # 解码照片数据
            photo_data = base64.b64decode(photo_data_b64)
            
            # 保存到数据库
            success = self.db.save_photo(task_id, photo_data, photo_filename)
            
            if success:
                self.photo_uploaded.emit(task_id, client_id)

                # 主动发送成功消息给客户端
                self._send_upload_result_to_client(client_id, True, "照片上传成功")

                # 任务完成后，为该客户端分配下一个任务
                self.assign_next_task_to_client(client_id)

                return {"status": "success", "message": "Photo uploaded and task completed"}
            else:
                # 主动发送失败消息给客户端
                self._send_upload_result_to_client(client_id, False, "保存照片失败")

                return {"status": "error", "message": "Failed to save photo"}
                
        except Exception as e:
            logger.error(f"上传照片失败: {e}")
            # 主动发送失败消息给客户端
            self._send_upload_result_to_client(client_id, False, f"上传异常: {str(e)}")
            return {"status": "error", "message": f"Upload failed: {str(e)}"}

    def _send_upload_result_to_client(self, client_id: str, success: bool, message: str):
        """向客户端发送上传结果"""
        try:
            if client_id in self.connected_clients:
                client_socket = self.connected_clients[client_id].get('socket')
                if client_socket:
                    result_message = {
                        "type": "upload_result",
                        "success": success,
                        "message": message
                    }

                    client_socket.send(json.dumps(result_message).encode('utf-8'))
                    logger.info(f"已向客户端 {client_id} 发送上传结果: {success}, {message}")

        except Exception as e:
            logger.error(f"发送上传结果失败: {e}")

    def cancel_task(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """取消任务"""
        try:
            task_id = message.get('task_id')
            if not task_id:
                return {"status": "error", "message": "Missing task_id"}
            
            success = self.db.cancel_task(task_id)
            
            if success:
                self.task_cancelled.emit(task_id)
                return {"status": "success", "message": "Task cancelled and deleted"}
            else:
                return {"status": "error", "message": "Task not found"}
                
        except Exception as e:
            logger.error(f"取消任务失败: {e}")
            return {"status": "error", "message": f"Cancel task failed: {str(e)}"}
    
    def get_client_info(self, client_id: str) -> Dict[str, Any]:
        """获取客户端信息"""
        if client_id in self.connected_clients:
            client_info = self.connected_clients[client_id]
            tasks = self.db.get_pc_client_tasks(client_id)
            
            return {
                "status": "success",
                "client_id": client_id,
                "address": client_info["address"],
                "connected_time": client_info["connected_time"].isoformat(),
                "tasks": tasks
            }
        else:
            return {"status": "error", "message": "Client not found"}
    
    def get_connected_clients_info(self) -> Dict[str, Any]:
        """获取所有连接的客户端信息"""
        clients_info = {}

        for client_id, info in self.connected_clients.items():
            # 格式化连接时间
            connected_time = info.get("connected_time", time.time())
            if isinstance(connected_time, (int, float)):
                time_str = datetime.fromtimestamp(connected_time).strftime("%Y-%m-%d %H:%M:%S")
            else:
                time_str = str(connected_time)

            # 获取未完成任务数量
            incomplete_tasks = self.db.get_pc_client_incomplete_tasks_count(client_id)
            total_tasks = len(self.db.get_pc_client_tasks(client_id))

            clients_info[client_id] = {
                "address": f"{info['address'][0]}:{info['address'][1]}",
                "connected_time": time_str,
                "tasks": total_tasks,
                "incomplete_tasks": incomplete_tasks,
                "station_number": info.get("station_number", 1)
            }

        return clients_info

    def load_photo_settings(self):
        """加载保存的照片设置"""
        try:
            if os.path.exists(config.PHOTO_SETTINGS_FILE):
                with open(config.PHOTO_SETTINGS_FILE, 'r', encoding='utf-8') as f:
                    saved_settings = json.load(f)
                    self.photo_settings.update(saved_settings)
                    logger.info("已加载保存的照片设置")
            else:
                logger.info("未找到保存的照片设置，使用默认设置")
        except Exception as e:
            logger.error(f"加载照片设置失败: {e}")

    def save_photo_settings(self):
        """保存照片设置到文件"""
        try:
            with open(config.PHOTO_SETTINGS_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.photo_settings, f, ensure_ascii=False, indent=2)
                logger.info("照片设置已保存")
        except Exception as e:
            logger.error(f"保存照片设置失败: {e}")

    def load_station_assignments(self):
        """加载机位号分配记录"""
        try:
            station_file = "station_assignments.json"
            if os.path.exists(station_file):
                with open(station_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.machine_stations = data.get("machine_stations", {})
                    self.next_station_number = data.get("next_station_number", 1)
                    logger.info(f"已加载机位号分配记录: {len(self.machine_stations)} 条")
            else:
                logger.info("未找到机位号分配记录，使用默认设置")
        except Exception as e:
            logger.error(f"加载机位号分配记录失败: {e}")

    def save_station_assignments(self):
        """保存机位号分配记录"""
        try:
            station_file = "station_assignments.json"
            data = {
                "machine_stations": self.machine_stations,
                "next_station_number": self.next_station_number
            }
            with open(station_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                logger.info("机位号分配记录已保存")
        except Exception as e:
            logger.error(f"保存机位号分配记录失败: {e}")

    def assign_station_by_machine_id(self, machine_id: str, client_id: str) -> int:
        """根据机器码分配机位号"""
        try:
            if not machine_id:
                logger.warning(f"客户端 {client_id} 没有提供机器码，分配默认机位号")
                return 1

            # 检查是否已经为这个机器码分配过机位号
            if machine_id in self.machine_stations:
                station_number = self.machine_stations[machine_id]
                self.client_stations[client_id] = station_number
                logger.info(f"机器码 {machine_id} 重用机位号: {station_number}")
                return station_number

            # 分配新的机位号
            station_number = self.next_station_number
            self.next_station_number += 1

            # 保存机器码到机位号的映射
            self.machine_stations[machine_id] = station_number
            self.client_stations[client_id] = station_number

            # 保存分配记录
            self.save_station_assignments()

            logger.info(f"为机器码 {machine_id} 分配新机位号: {station_number}")
            return station_number

        except Exception as e:
            logger.error(f"根据机器码分配机位号失败: {e}")
            return 1  # 默认返回1号机位

    def update_client_station_number(self, client_id: str, new_station_number: int):
        """更新客户端机位号并同步到PC端"""
        try:
            if client_id in self.connected_clients:
                old_station = self.connected_clients[client_id].get('station_number', 0)
                self.connected_clients[client_id]['station_number'] = new_station_number

                # 发送机位号更新消息给PC端
                client_socket = self.connected_clients[client_id].get('socket')
                if client_socket:
                    update_message = {
                        "type": "station_number_update",
                        "station_number": new_station_number
                    }
                    try:
                        client_socket.send(json.dumps(update_message).encode('utf-8'))
                        logger.info(f"已向客户端 {client_id} 发送机位号更新: {old_station} -> {new_station_number}")
                    except Exception as e:
                        logger.error(f"向客户端 {client_id} 发送机位号更新失败: {e}")

                return True
            else:
                logger.warning(f"客户端 {client_id} 不在连接列表中")
                return False

        except Exception as e:
            logger.error(f"更新客户端机位号失败: {e}")
            return False

    def assign_station_number(self, client_id: str, address: tuple) -> int:
        """为客户端分配机位号"""
        try:
            # 根据IP地址生成唯一标识
            client_ip = address[0]

            # 检查是否已经为这个IP分配过机位号（查找活跃的分配）
            for existing_client_id, client_info in self.connected_clients.items():
                if (existing_client_id != client_id and
                    client_info.get('address', [''])[0] == client_ip and
                    'station_number' in client_info):
                    # 找到了相同IP的活跃连接，重用机位号
                    station_num = client_info['station_number']
                    self.client_stations[client_id] = station_num
                    logger.info(f"为客户端 {client_id} ({client_ip}) 重用活跃机位号: {station_num}")
                    return station_num

            # 检查历史分配记录
            for existing_client_id, station_num in self.client_stations.items():
                if existing_client_id.startswith(f"station_{client_ip}_"):
                    # 找到了相同IP的历史记录，重用机位号
                    self.client_stations[client_id] = station_num
                    logger.info(f"为客户端 {client_id} ({client_ip}) 重用历史机位号: {station_num}")
                    return station_num

            # 没有找到记录，分配新的机位号
            station_number = self.next_station_number
            self.next_station_number += 1

            # 保存机位号分配记录
            station_key = f"station_{client_ip}_{station_number}"
            self.client_stations[station_key] = station_number
            self.client_stations[client_id] = station_number

            logger.info(f"为客户端 {client_id} ({client_ip}) 分配新机位号: {station_number}")
            return station_number

        except Exception as e:
            logger.error(f"分配机位号失败: {e}")
            return 1  # 默认返回1号机位

    def get_client_station_number(self, client_id: str) -> int:
        """获取客户端的机位号"""
        return self.client_stations.get(client_id, 1)
