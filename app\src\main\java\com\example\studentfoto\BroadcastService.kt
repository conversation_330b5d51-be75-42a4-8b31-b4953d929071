package com.example.studentfoto

import android.content.Context
import android.net.wifi.WifiManager
import android.util.Log
import org.json.JSONObject
import java.net.*
import java.util.concurrent.atomic.AtomicBoolean

/**
 * UDP广播服务
 * 用于向局域网广播设备信息，方便PC端发现设备
 */
class BroadcastService(private val context: Context) {
    
    private val TAG = "BroadcastService"
    
    // 广播配置
    private val BROADCAST_PORT = 8081
    private val BROADCAST_INTERVAL = 3000L // 3秒广播一次
    
    // 运行状态
    private val isRunning = AtomicBoolean(false)
    private var broadcastThread: Thread? = null
    private var socket: DatagramSocket? = null
    
    // 设备信息
    private var deviceName: String = "Android设备"
    private var deviceIp: String = ""
    private var tcpPort: Int = 8080
    
    fun start(deviceName: String, deviceIp: String, tcpPort: Int) {
        if (isRunning.get()) {
            Log.d(TAG, "Broadcast service already running")
            return
        }
        
        this.deviceName = deviceName
        this.deviceIp = deviceIp
        this.tcpPort = tcpPort
        
        isRunning.set(true)
        
        broadcastThread = Thread {
            runBroadcast()
        }
        broadcastThread?.start()
        
        Log.d(TAG, "Broadcast service started")
    }
    
    fun stop() {
        if (!isRunning.get()) {
            return
        }
        
        isRunning.set(false)
        
        try {
            socket?.close()
            broadcastThread?.interrupt()
            broadcastThread?.join(1000)
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping broadcast service: ${e.message}")
        }
        
        Log.d(TAG, "Broadcast service stopped")
    }
    
    fun updateDeviceInfo(deviceName: String, deviceIp: String, tcpPort: Int) {
        this.deviceName = deviceName
        this.deviceIp = deviceIp
        this.tcpPort = tcpPort
        Log.d(TAG, "Device info updated: $deviceName @ $deviceIp:$tcpPort")
    }
    
    private fun runBroadcast() {
        try {
            socket = DatagramSocket()
            socket?.broadcast = true
            
            // 获取WiFi多播锁
            val wifiManager = context.getSystemService(Context.WIFI_SERVICE) as WifiManager
            val multicastLock = wifiManager.createMulticastLock("StudentFotoBroadcast")
            multicastLock.acquire()
            
            Log.d(TAG, "Broadcast thread started")
            
            while (isRunning.get()) {
                try {
                    sendBroadcast()
                    Thread.sleep(BROADCAST_INTERVAL)
                } catch (e: InterruptedException) {
                    break
                } catch (e: Exception) {
                    Log.e(TAG, "Error in broadcast loop: ${e.message}")
                    Thread.sleep(1000) // 出错时等待1秒再重试
                }
            }
            
            multicastLock.release()
            
        } catch (e: Exception) {
            Log.e(TAG, "Broadcast thread error: ${e.message}")
        } finally {
            socket?.close()
            Log.d(TAG, "Broadcast thread stopped")
        }
    }
    
    private fun sendBroadcast() {
        try {
            // 构建广播消息
            val broadcastData = JSONObject().apply {
                put("type", "STUDENT_FOTO_DEVICE")
                put("deviceName", deviceName)
                put("deviceIp", deviceIp)
                put("tcpPort", tcpPort)
                put("timestamp", System.currentTimeMillis())
                put("version", "1.0")
            }
            
            val message = broadcastData.toString()
            val messageBytes = message.toByteArray()
            
            // 发送到广播地址
            val broadcastAddresses = getBroadcastAddresses()
            
            for (broadcastAddress in broadcastAddresses) {
                try {
                    val packet = DatagramPacket(
                        messageBytes,
                        messageBytes.size,
                        broadcastAddress,
                        BROADCAST_PORT
                    )
                    
                    socket?.send(packet)
                    Log.d(TAG, "Broadcast sent to ${broadcastAddress.hostAddress}: $message")
                    
                } catch (e: Exception) {
                    Log.w(TAG, "Failed to send broadcast to ${broadcastAddress.hostAddress}: ${e.message}")
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error sending broadcast: ${e.message}")
        }
    }
    
    private fun getBroadcastAddresses(): List<InetAddress> {
        val broadcastAddresses = mutableListOf<InetAddress>()
        
        try {
            // 添加通用广播地址
            broadcastAddresses.add(InetAddress.getByName("***************"))
            
            // 获取所有网络接口的广播地址
            val interfaces = NetworkInterface.getNetworkInterfaces()
            while (interfaces.hasMoreElements()) {
                val networkInterface = interfaces.nextElement()
                
                if (networkInterface.isLoopback || !networkInterface.isUp) {
                    continue
                }
                
                for (interfaceAddress in networkInterface.interfaceAddresses) {
                    val broadcast = interfaceAddress.broadcast
                    if (broadcast != null) {
                        broadcastAddresses.add(broadcast)
                        Log.d(TAG, "Found broadcast address: ${broadcast.hostAddress}")
                    }
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error getting broadcast addresses: ${e.message}")
        }
        
        return broadcastAddresses
    }
    
    fun isRunning(): Boolean {
        return isRunning.get()
    }
}
