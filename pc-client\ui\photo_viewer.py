#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
照片查看器组件
"""

import os
import base64
import io
import tempfile
import re
import time
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QListWidget, QListWidgetItem, QPushButton,
                             QScrollArea, QSizePolicy, QMessageBox, QFileDialog,
                             QProgressDialog, QApplication)
from PyQt5.QtCore import Qt, QSize, QThread, pyqtSignal, QObject, QTimer
from PyQt5.QtGui import QPixmap, QIcon
from PIL import Image

from core.hivision_client import get_hivision_client
from core.photo_settings import get_photo_settings_manager

def fix_base64_padding(base64_str: str) -> str:
    """修复base64填充问题"""
    if not base64_str:
        return ""

    # 去除空白字符
    base64_str = base64_str.strip()

    # 移除可能的data URL前缀
    if base64_str.startswith('data:'):
        base64_str = base64_str.split(',', 1)[-1]

    # 移除非base64字符
    base64_str = re.sub(r'[^A-Za-z0-9+/=]', '', base64_str)

    # 确保长度是4的倍数
    missing_padding = len(base64_str) % 4
    if missing_padding:
        base64_str += '=' * (4 - missing_padding)

    return base64_str

class IDPhotoProcessThread(QThread):
    """证件照处理线程"""

    progress_updated = pyqtSignal(str)  # 进度更新信号
    processing_finished = pyqtSignal(dict)  # 处理完成信号
    error_occurred = pyqtSignal(str)  # 错误信号
    log_message = pyqtSignal(str)  # 日志消息信号

    def __init__(self, image_path: str):
        super().__init__()
        self.image_path = image_path
        self.hivision_client = get_hivision_client()
        self.settings_manager = get_photo_settings_manager()
        self.error_messages = []  # 记录所有错误消息

    def run(self):
        """运行证件照处理"""
        try:
            settings = self.settings_manager.get_current_settings()
            self.log_message.emit(f"开始处理证件照: {os.path.basename(self.image_path)}")
            self.log_message.emit(f"使用设置: {settings.size.name}, {settings.background_color.name}, {settings.matting_model.name}")

            # 步骤1: 生成证件照（底透明）
            self.progress_updated.emit("正在生成证件照...")
            self.log_message.emit("步骤1: 调用证件照生成API")

            result = self.hivision_client.create_id_photo(
                image_input=self.image_path,
                height=settings.size.height,
                width=settings.size.width,
                human_matting_model=settings.matting_model.model_id,
                face_detect_model=settings.face_detect_model.model_id,
                hd=settings.hd,
                dpi=settings.dpi,
                face_alignment=settings.face_alignment,
                head_measure_ratio=settings.head_measure_ratio,
                head_height_ratio=settings.head_height_ratio,
                top_distance_max=settings.top_distance_max,
                top_distance_min=settings.top_distance_min,
                brightness_strength=settings.brightness_strength,
                contrast_strength=settings.contrast_strength,
                sharpen_strength=settings.sharpen_strength,
                saturation_strength=settings.saturation_strength,
            )

            if "error" in result:
                error_msg = f"证件照生成失败: {result['error']}"
                self.error_messages.append(error_msg)
                self.log_message.emit(error_msg)
                self.error_occurred.emit(self._format_error_summary())
                return

            self.log_message.emit("步骤1完成: 证件照生成成功")
            has_standard = "image_base64_standard" in result
            has_hd = "image_base64_hd" in result
            self.log_message.emit(f"获得结果: 标准照={has_standard}, 高清照={has_hd}")

            # 检查是否真的生成了图像数据
            if not has_standard and not has_hd:
                error_msg = "API返回成功但未生成任何图像数据，可能是照片中没有检测到人脸或图像质量不佳"
                self.error_messages.append(error_msg)
                self.log_message.emit(error_msg)
                self.error_occurred.emit(self._format_error_summary())
                return

            # 步骤2: 添加背景色
            self.progress_updated.emit("正在添加背景色...")
            self.log_message.emit("步骤2: 添加背景色")

            if has_standard:
                try:
                    self.log_message.emit("处理标准证件照背景...")
                    # 修复base64填充问题
                    base64_data = fix_base64_padding(result["image_base64_standard"])
                    self.log_message.emit(f"标准照base64修复后长度: {len(base64_data)}")
                    image_bytes = base64.b64decode(base64_data)

                    bg_result = self.hivision_client.add_background(
                        image_input=image_bytes,
                        color=settings.background_color.hex_value,
                        kb=settings.kb_size,
                        render=settings.render_mode,
                        dpi=settings.dpi
                    )

                    if "error" in bg_result:
                        error_msg = f"标准证件照背景处理失败: {bg_result['error']}"
                        self.error_messages.append(error_msg)
                        self.log_message.emit(error_msg)
                    else:
                        result["image_base64_standard_with_bg"] = bg_result.get("image_base64")
                        self.log_message.emit("标准证件照背景处理成功")

                except Exception as e:
                    error_msg = f"标准证件照背景处理异常: {str(e)}"
                    self.error_messages.append(error_msg)
                    self.log_message.emit(error_msg)

            if has_hd and settings.hd:
                try:
                    self.log_message.emit("处理高清证件照背景...")
                    # 修复base64填充问题
                    base64_data = fix_base64_padding(result["image_base64_hd"])
                    self.log_message.emit(f"高清照base64修复后长度: {len(base64_data)}")
                    image_bytes = base64.b64decode(base64_data)

                    bg_result_hd = self.hivision_client.add_background(
                        image_input=image_bytes,
                        color=settings.background_color.hex_value,
                        kb=settings.kb_size,
                        render=settings.render_mode,
                        dpi=settings.dpi
                    )

                    if "error" in bg_result_hd:
                        error_msg = f"高清证件照背景处理失败: {bg_result_hd['error']}"
                        self.error_messages.append(error_msg)
                        self.log_message.emit(error_msg)
                    else:
                        result["image_base64_hd_with_bg"] = bg_result_hd.get("image_base64")
                        self.log_message.emit("高清证件照背景处理成功")

                except Exception as e:
                    error_msg = f"高清证件照背景处理异常: {str(e)}"
                    self.error_messages.append(error_msg)
                    self.log_message.emit(error_msg)

            # 添加错误信息到结果中
            if self.error_messages:
                result["error_messages"] = self.error_messages
                result["has_errors"] = True
                self.log_message.emit(f"处理完成，但有 {len(self.error_messages)} 个错误")
            else:
                result["has_errors"] = False
                self.log_message.emit("处理完成，无错误")

            self.progress_updated.emit("处理完成")
            self.processing_finished.emit(result)

        except Exception as e:
            error_msg = f"证件照处理异常: {str(e)}"
            self.error_messages.append(error_msg)
            self.log_message.emit(error_msg)
            self.error_occurred.emit(self._format_error_summary())

    def _format_error_summary(self) -> str:
        """格式化错误摘要"""
        if not self.error_messages:
            return "未知错误"

        summary = f"证件照处理失败 ({len(self.error_messages)} 个错误):\n\n"
        for i, error in enumerate(self.error_messages, 1):
            summary += f"{i}. {error}\n"

        return summary.strip()

class PhotoViewer(QWidget):
    """照片查看器类"""

    # 信号定义
    processing_finished = pyqtSignal(bool, str)  # 处理完成信号 (成功, 消息)

    def __init__(self, phone_controller=None, parent=None):
        super().__init__(parent)
        self.photos = []  # 存储照片路径列表
        self.current_photo_index = -1
        self.phone_controller = phone_controller
        self.last_processed_photo_path = None  # 最后处理的照片路径
        self.init_ui()

    def find_main_window(self):
        """通过parent链查找MainWindow"""
        widget = self
        while widget:
            # 检查是否是MainWindow（通过类名或特定属性）
            if hasattr(widget, 'phone_controller') and hasattr(widget, 'server_client'):
                return widget
            widget = widget.parent()
        return None
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("照片查看器")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                padding: 10px;
                background-color: #f0f0f0;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # 主要内容区域
        content_layout = QHBoxLayout()
        
        # 左侧照片列表
        list_widget = QWidget()
        list_layout = QVBoxLayout(list_widget)
        list_layout.setContentsMargins(0, 0, 0, 0)
        
        list_label = QLabel("照片列表")
        list_label.setStyleSheet("font-weight: bold; margin-bottom: 5px;")
        list_layout.addWidget(list_label)
        
        self.photo_list = QListWidget()
        self.photo_list.setMaximumWidth(200)
        self.photo_list.itemClicked.connect(self.on_photo_selected)
        list_layout.addWidget(self.photo_list)
        
        # 操作按钮
        btn_layout = QVBoxLayout()
        
        self.save_btn = QPushButton("💾 保存照片")
        self.save_btn.setEnabled(False)
        self.save_btn.clicked.connect(self.save_current_photo)
        btn_layout.addWidget(self.save_btn)
        
        self.delete_btn = QPushButton("🗑️ 删除照片")
        self.delete_btn.setEnabled(False)
        self.delete_btn.clicked.connect(self.delete_current_photo)
        btn_layout.addWidget(self.delete_btn)
        
        self.clear_btn = QPushButton("🧹 清空列表")
        self.clear_btn.clicked.connect(self.clear_photos)
        btn_layout.addWidget(self.clear_btn)

        # 证件照处理按钮
        self.id_photo_btn = QPushButton("🆔 制作证件照")
        self.id_photo_btn.setEnabled(False)
        self.id_photo_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        self.id_photo_btn.clicked.connect(self.process_id_photo)
        btn_layout.addWidget(self.id_photo_btn)

        btn_layout.addStretch()
        list_layout.addLayout(btn_layout)
        
        content_layout.addWidget(list_widget)
        
        # 右侧照片显示区域
        display_widget = QWidget()
        display_layout = QVBoxLayout(display_widget)
        display_layout.setContentsMargins(0, 0, 0, 0)
        
        display_label = QLabel("照片预览")
        display_label.setStyleSheet("font-weight: bold; margin-bottom: 5px;")
        display_layout.addWidget(display_label)
        
        # 滚动区域用于显示大图
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setAlignment(Qt.AlignCenter)
        
        self.photo_label = QLabel()
        self.photo_label.setAlignment(Qt.AlignCenter)
        self.photo_label.setStyleSheet("""
            QLabel {
                background-color: #f8f8f8;
                border: 2px dashed #cccccc;
                border-radius: 10px;
                min-height: 400px;
            }
        """)
        self.photo_label.setText("暂无照片\n\n点击左侧列表中的照片进行查看")
        
        self.scroll_area.setWidget(self.photo_label)
        display_layout.addWidget(self.scroll_area)
        
        # 照片信息
        self.info_label = QLabel("照片信息: 无")
        self.info_label.setStyleSheet("""
            QLabel {
                background-color: #e8f4f8;
                padding: 8px;
                border-radius: 4px;
                margin-top: 5px;
            }
        """)
        display_layout.addWidget(self.info_label)
        
        content_layout.addWidget(display_widget)
        
        # 设置比例
        content_layout.setStretch(0, 1)  # 左侧列表
        content_layout.setStretch(1, 3)  # 右侧显示区域
        
        layout.addLayout(content_layout)

    def add_photo(self, photo_path):
        """添加照片到列表"""
        if photo_path and os.path.exists(photo_path):
            self.photos.append(photo_path)
            
            # 创建列表项
            item = QListWidgetItem()
            filename = os.path.basename(photo_path)
            item.setText(filename)
            item.setData(Qt.UserRole, photo_path)
            
            # 创建缩略图
            try:
                pixmap = QPixmap(photo_path)
                if not pixmap.isNull():
                    # 创建缩略图
                    thumbnail = pixmap.scaled(64, 64, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                    item.setIcon(QIcon(thumbnail))
            except Exception as e:
                print(f"创建缩略图失败: {e}")
            
            self.photo_list.addItem(item)
            
            # 自动选择新添加的照片
            self.photo_list.setCurrentItem(item)
            self.on_photo_selected(item)
            
    def on_photo_selected(self, item):
        """照片被选中时的处理"""
        if item:
            photo_path = item.data(Qt.UserRole)
            self.current_photo_index = self.photo_list.row(item)
            self.display_photo(photo_path)
            self.save_btn.setEnabled(True)
            self.delete_btn.setEnabled(True)
            self.id_photo_btn.setEnabled(True)
            
    def display_photo(self, photo_path):
        """显示照片"""
        try:
            pixmap = QPixmap(photo_path)
            if not pixmap.isNull():
                # 缩放照片以适应显示区域
                scaled_pixmap = pixmap.scaled(
                    self.scroll_area.size() * 0.9,
                    Qt.KeepAspectRatio,
                    Qt.SmoothTransformation
                )
                self.photo_label.setPixmap(scaled_pixmap)
                self.photo_label.setStyleSheet("")
                
                # 更新照片信息
                file_size = os.path.getsize(photo_path)
                file_size_mb = file_size / (1024 * 1024)
                info_text = f"文件: {os.path.basename(photo_path)} | "
                info_text += f"尺寸: {pixmap.width()}x{pixmap.height()} | "
                info_text += f"大小: {file_size_mb:.2f} MB"
                self.info_label.setText(info_text)
            else:
                self.photo_label.setText("无法加载照片")
                self.info_label.setText("照片信息: 加载失败")
        except Exception as e:
            self.photo_label.setText(f"加载照片时出错:\n{str(e)}")
            self.info_label.setText("照片信息: 加载失败")
            
    def save_current_photo(self):
        """保存当前照片"""
        if self.current_photo_index >= 0:
            current_item = self.photo_list.currentItem()
            if current_item:
                source_path = current_item.data(Qt.UserRole)
                filename = os.path.basename(source_path)
                
                # 打开文件保存对话框
                save_path, _ = QFileDialog.getSaveFileName(
                    self,
                    "保存照片",
                    filename,
                    "图片文件 (*.jpg *.jpeg *.png *.bmp)"
                )
                
                if save_path:
                    try:
                        # 复制文件
                        import shutil
                        shutil.copy2(source_path, save_path)
                        QMessageBox.information(self, "成功", f"照片已保存到:\n{save_path}")
                    except Exception as e:
                        QMessageBox.critical(self, "错误", f"保存照片失败:\n{str(e)}")
                        
    def delete_current_photo(self):
        """删除当前照片"""
        if self.current_photo_index >= 0:
            current_item = self.photo_list.currentItem()
            if current_item:
                reply = QMessageBox.question(
                    self,
                    "确认删除",
                    "确定要从列表中删除这张照片吗？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )
                
                if reply == QMessageBox.Yes:
                    # 从列表中移除
                    row = self.photo_list.row(current_item)
                    self.photo_list.takeItem(row)
                    self.photos.pop(row)
                    
                    # 清空显示
                    if self.photo_list.count() == 0:
                        self.photo_label.clear()
                        self.photo_label.setText("暂无照片\n\n点击左侧列表中的照片进行查看")
                        self.photo_label.setStyleSheet("""
                            QLabel {
                                background-color: #f8f8f8;
                                border: 2px dashed #cccccc;
                                border-radius: 10px;
                                min-height: 400px;
                            }
                        """)
                        self.info_label.setText("照片信息: 无")
                        self.save_btn.setEnabled(False)
                        self.delete_btn.setEnabled(False)
                        self.id_photo_btn.setEnabled(False)
                        self.current_photo_index = -1
                    else:
                        # 选择下一张照片
                        if row < self.photo_list.count():
                            self.photo_list.setCurrentRow(row)
                        else:
                            self.photo_list.setCurrentRow(row - 1)
                        self.on_photo_selected(self.photo_list.currentItem())
                        
    def clear_photos(self):
        """清空照片列表"""
        if self.photo_list.count() > 0:
            reply = QMessageBox.question(
                self,
                "确认清空",
                "确定要清空所有照片吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                self.photo_list.clear()
                self.photos.clear()
                self.photo_label.clear()
                self.photo_label.setText("暂无照片\n\n点击左侧列表中的照片进行查看")
                self.photo_label.setStyleSheet("""
                    QLabel {
                        background-color: #f8f8f8;
                        border: 2px dashed #cccccc;
                        border-radius: 10px;
                        min-height: 400px;
                    }
                """)
                self.info_label.setText("照片信息: 无")
                self.save_btn.setEnabled(False)
                self.delete_btn.setEnabled(False)
                self.id_photo_btn.setEnabled(False)
                self.current_photo_index = -1

    def refresh_photos(self):
        """刷新照片列表"""
        # 清空当前列表
        self.photo_list.clear()
        self.photos.clear()
        self.photo_label.clear()
        self.photo_label.setText("暂无照片\n\n点击左侧列表中的照片进行查看")
        self.photo_label.setStyleSheet("""
            QLabel {
                background-color: #f8f8f8;
                border: 2px dashed #cccccc;
                border-radius: 10px;
                min-height: 400px;
            }
        """)
        self.info_label.setText("照片信息: 无")
        self.save_btn.setEnabled(False)
        self.delete_btn.setEnabled(False)
        self.id_photo_btn.setEnabled(False)
        self.current_photo_index = -1

        # 记录刷新日志
        self.on_process_log("照片列表已刷新")

    def process_id_photo(self):
        """处理证件照"""
        if self.current_photo_index < 0:
            QMessageBox.warning(self, "提示", "请先选择一张照片")
            return

        current_item = self.photo_list.currentItem()
        if not current_item:
            return

        photo_path = current_item.data(Qt.UserRole)

        # 检查API服务连接
        hivision_client = get_hivision_client()
        if not hivision_client.check_service():
            QMessageBox.critical(
                self, "服务不可用",
                "无法连接到HivisionIDPhotos API服务。\n\n"
                "请确保API服务正在运行。"
            )
            return

        # 创建进度对话框
        self.progress_dialog = QProgressDialog("正在处理证件照...", "取消", 0, 0, self)
        self.progress_dialog.setWindowModality(Qt.WindowModal)
        self.progress_dialog.setAutoClose(False)
        self.progress_dialog.setAutoReset(False)
        self.progress_dialog.show()

        # 创建处理线程
        self.process_thread = IDPhotoProcessThread(photo_path)
        self.process_thread.progress_updated.connect(self.on_process_progress)
        self.process_thread.processing_finished.connect(self.on_process_finished)
        self.process_thread.error_occurred.connect(self.on_process_error)
        self.process_thread.log_message.connect(self.on_process_log)
        self.process_thread.finished.connect(self.on_thread_finished)

        # 连接取消按钮
        self.progress_dialog.canceled.connect(self.cancel_processing)

        # 启动处理
        self.process_thread.start()

    def on_process_progress(self, message: str):
        """处理进度更新"""
        if hasattr(self, 'progress_dialog'):
            self.progress_dialog.setLabelText(message)
            QApplication.processEvents()

    def on_process_log(self, message: str):
        """处理日志消息"""
        # 发送日志消息到主窗口
        if hasattr(self.parent(), 'add_log'):
            self.parent().add_log(f"[证件照] {message}")
        else:
            print(f"[证件照] {message}")

    def on_process_finished(self, result: dict):
        """处理完成"""
        self.on_process_log(f"调试：on_process_finished被调用，result = {result}")
        try:
            if hasattr(self, 'progress_dialog'):
                self.progress_dialog.close()

            # 检查是否有处理后的照片路径（来自AI处理线程）
            processed_photo_path = result.get("processed_photo_path")
            if processed_photo_path:
                self.on_process_log(f"更新最后处理的照片路径: {processed_photo_path}")
                self.last_processed_photo_path = processed_photo_path

            # 检查处理是否成功
            success = result.get("success", True)  # 默认为True以兼容旧格式

            if not success:
                # AI处理失败（如人脸检测失败）
                error_msg = result.get("error", "AI处理失败")
                self.on_process_log(f"AI处理失败: {error_msg}")

                # 通知手机端处理失败
                main_window = self.find_main_window()
                is_mobile_connected = (main_window and hasattr(main_window, 'phone_controller')
                                     and main_window.phone_controller and main_window.phone_controller.is_connected())

                if is_mobile_connected:
                    self.on_process_log("手机已连接：通知手机处理失败")
                    self.notify_mobile_processing_error(error_msg)

                return  # 直接返回，不继续处理

            # 检查是否有错误
            has_errors = result.get("has_errors", False)
            error_messages = result.get("error_messages", [])

            # 保存处理结果
            saved_count = self.save_id_photo_results(result)

            # 检查是否连接了手机
            main_window = self.find_main_window()
            is_mobile_connected = (main_window and hasattr(main_window, 'phone_controller')
                                 and main_window.phone_controller and main_window.phone_controller.is_connected())

            # 显示结果消息
            if has_errors:
                # 有错误但可能有部分成功
                error_summary = "\n".join([f"• {msg}" for msg in error_messages])
                if saved_count > 0:
                    self.on_process_log(f"证件照处理部分成功！已保存 {saved_count} 张照片，遇到的问题：{error_summary}")
                    # 部分成功时仍然通知手机
                    if is_mobile_connected:
                        self.on_process_log("手机已连接：通知手机处理完成（部分成功）")
                        self.notify_mobile_processing_complete()
                else:
                    self.on_process_log(f"证件照处理失败！错误详情：{error_summary}")
                    # 处理失败时通知手机错误
                    if is_mobile_connected:
                        self.on_process_log("手机已连接：通知手机处理失败")
                        self.notify_mobile_processing_error(error_summary)

                # 记录详细错误日志
                self.on_process_log(f"处理完成，保存了 {saved_count} 张照片，遇到 {len(error_messages)} 个错误")
                for error in error_messages:
                    self.on_process_log(f"错误: {error}")
            else:
                # 完全成功
                self.on_process_log(f"处理完成，成功保存了 {saved_count} 张照片")

                # 通知手机处理完成
                if is_mobile_connected:
                    self.on_process_log("手机已连接：通知手机处理完成")
                    self.notify_mobile_processing_complete()
                else:
                    self.on_process_log("手机未连接，跳过通知")

                # 发出处理完成信号
                self.processing_finished.emit(True, f"成功保存了 {saved_count} 张照片")

        except Exception as e:
            error_msg = f"保存证件照结果失败：{str(e)}"
            self.on_process_log(f"保存失败: {error_msg}")

            # 发出处理失败信号
            self.processing_finished.emit(False, error_msg)

    def notify_mobile_processing_complete(self):
        """通知手机端处理完成，等待确认后发送照片"""
        try:
            # 通过MainWindow的手机控制器发送处理完成通知
            main_window = self.find_main_window()
            if main_window and hasattr(main_window, 'phone_controller') and main_window.phone_controller:
                main_window.phone_controller.send_processing_complete_notification()
                self.on_process_log("已通知手机端处理完成，等待确认...")
            else:
                self.on_process_log("未找到MainWindow或手机控制器，无法发送通知")
        except Exception as e:
            self.on_process_log(f"发送处理完成通知失败: {e}")

    def notify_mobile_processing_error(self, error_message: str):
        """通知手机端处理失败"""
        try:
            main_window = self.find_main_window()
            if main_window and hasattr(main_window, 'phone_controller') and main_window.phone_controller:
                # 发送错误消息到手机端
                error_command = f"ERROR:{error_message}\n"
                main_window.phone_controller.socket.send(error_command.encode('utf-8'))
                self.on_process_log(f"已通知手机端处理失败: {error_message}")
            else:
                self.on_process_log("未找到MainWindow或手机控制器，无法发送错误通知")
        except Exception as e:
            self.on_process_log(f"发送处理失败通知失败: {e}")

    def send_processed_photo_to_mobile(self):
        """发送处理后的照片到手机端（在收到确认后调用）"""
        try:
            # 获取最新处理的照片路径
            processed_photo_path = self.get_processed_photo_path()
            if processed_photo_path and os.path.exists(processed_photo_path):
                self.on_process_log(f"发送照片到手机: {processed_photo_path}")

                # 通过MainWindow的手机控制器直接发送照片数据
                main_window = self.find_main_window()
                if main_window and hasattr(main_window, 'phone_controller') and main_window.phone_controller:
                    main_window.phone_controller.send_photo_data_directly(processed_photo_path)
                    self.on_process_log("照片已发送到手机端")
                else:
                    self.on_process_log("未找到MainWindow或手机控制器，无法发送照片")
            else:
                self.on_process_log("未找到处理后的照片文件")
        except Exception as e:
            self.on_process_log(f"发送照片到手机失败: {e}")

    def get_processed_photo_path(self):
        """获取最新处理的照片路径"""
        try:
            # 获取ID照片保存目录
            id_dir = os.path.join(os.path.dirname(self.photo_path), "..", "id")
            if os.path.exists(id_dir):
                # 获取最新的照片文件
                photo_files = [f for f in os.listdir(id_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
                if photo_files:
                    # 按修改时间排序，获取最新的
                    photo_files.sort(key=lambda x: os.path.getmtime(os.path.join(id_dir, x)), reverse=True)
                    latest_photo = os.path.join(id_dir, photo_files[0])
                    return latest_photo
        except Exception as e:
            self.on_process_log(f"获取处理照片路径失败: {e}")
        return None

    def on_process_error(self, error_message: str):
        """处理错误"""
        if hasattr(self, 'progress_dialog'):
            self.progress_dialog.close()

        # 记录错误日志
        self.on_process_log(f"处理失败: {error_message}")

        # 删除已拍摄的原始照片
        self.delete_failed_photo()

        # 显示失败提示
        self.show_error_message(f"照片处理失败: {error_message}")

        # 通知手机端处理失败
        if self.phone_controller and self.phone_controller.is_connected():
            self.on_process_log("手机已连接：通知手机处理失败")
            self.notify_mobile_processing_error(error_message)
        else:
            self.on_process_log("手机未连接，无法通知处理失败")

        # 等待1秒后自动恢复到等待拍摄状态
        QTimer.singleShot(1000, self.reset_to_waiting_state)

    def delete_failed_photo(self):
        """删除处理失败的原始照片"""
        try:
            # 如果有当前选中的照片，删除它
            if self.current_photo_index >= 0 and self.current_photo_index < len(self.photos):
                photo_path = self.photos[self.current_photo_index]

                # 删除文件
                if os.path.exists(photo_path):
                    os.remove(photo_path)
                    self.on_process_log(f"已删除失败的照片文件: {os.path.basename(photo_path)}")

                # 从列表中移除
                self.photos.pop(self.current_photo_index)
                self.photo_list.takeItem(self.current_photo_index)

                # 重置界面状态
                self.current_photo_index = -1
                self.photo_label.clear()
                self.photo_label.setText("暂无照片\n\n等待手机端拍摄...")
                self.photo_label.setStyleSheet("""
                    QLabel {
                        background-color: #f8f8f8;
                        border: 2px dashed #cccccc;
                        border-radius: 10px;
                        min-height: 400px;
                    }
                """)
                self.info_label.setText("照片信息: 无")
                self.save_btn.setEnabled(False)
                self.delete_btn.setEnabled(False)
                self.id_photo_btn.setEnabled(False)

        except Exception as e:
            self.on_process_log(f"删除失败照片时出错: {str(e)}")

    def show_error_message(self, message: str):
        """显示错误消息（非阻塞）"""
        # 在状态栏或日志中显示错误，不使用阻塞对话框
        self.on_process_log(f"错误提示: {message}")

        # 可以在照片显示区域临时显示错误信息
        self.photo_label.setText(f"处理失败\n\n{message}\n\n1秒后自动恢复...")
        self.photo_label.setStyleSheet("""
            QLabel {
                background-color: #ffe6e6;
                border: 2px solid #ff6666;
                border-radius: 10px;
                min-height: 400px;
                color: #cc0000;
                font-weight: bold;
            }
        """)

    def reset_to_waiting_state(self):
        """重置到等待拍摄状态"""
        self.photo_label.setText("等待手机端拍摄...")
        self.photo_label.setStyleSheet("""
            QLabel {
                background-color: #f8f8f8;
                border: 2px dashed #cccccc;
                border-radius: 10px;
                min-height: 400px;
                color: #666666;
            }
        """)
        self.on_process_log("已重置到等待拍摄状态")

    def on_thread_finished(self):
        """线程结束清理"""
        if hasattr(self, 'process_thread'):
            self.process_thread.deleteLater()
            delattr(self, 'process_thread')

    def cancel_processing(self):
        """取消处理"""
        if hasattr(self, 'process_thread') and self.process_thread.isRunning():
            self.process_thread.terminate()
            self.process_thread.wait()

        if hasattr(self, 'progress_dialog'):
            self.progress_dialog.close()

    def save_id_photo_results(self, result: dict) -> int:
        """
        保存证件照处理结果

        Returns:
            int: 成功保存的文件数量
        """
        settings = get_photo_settings_manager().get_current_settings()
        saved_count = 0

        try:
            # 保存标准证件照（带背景）
            if "image_base64_standard_with_bg" in result:
                try:
                    standard_path = self.save_base64_image(
                        result["image_base64_standard_with_bg"],
                        f"证件照_标准_{settings.size.name}_{settings.background_color.name}.jpg"
                    )
                    if standard_path:
                        self.add_photo(standard_path)
                        saved_count += 1
                        self.last_processed_photo_path = standard_path  # 记录最后处理的照片
                        self.on_process_log(f"保存标准证件照: {os.path.basename(standard_path)}")
                    else:
                        self.on_process_log("保存标准证件照失败: 文件保存错误")
                except Exception as e:
                    self.on_process_log(f"保存标准证件照异常: {str(e)}")

            # 保存高清证件照（带背景）
            if "image_base64_hd_with_bg" in result and settings.hd:
                try:
                    hd_path = self.save_base64_image(
                        result["image_base64_hd_with_bg"],
                        f"证件照_高清_{settings.size.name}_{settings.background_color.name}.jpg"
                    )
                    if hd_path:
                        self.add_photo(hd_path)
                        saved_count += 1
                        self.on_process_log(f"保存高清证件照: {os.path.basename(hd_path)}")
                    else:
                        self.on_process_log("保存高清证件照失败: 文件保存错误")
                except Exception as e:
                    self.on_process_log(f"保存高清证件照异常: {str(e)}")

            # 如果没有带背景的照片，尝试保存透明背景的照片
            if saved_count == 0:
                self.on_process_log("没有带背景的照片，尝试保存透明背景照片")

                # 保存标准证件照（透明背景）
                if "image_base64_standard" in result:
                    try:
                        standard_path = self.save_base64_image(
                            result["image_base64_standard"],
                            f"证件照_标准_透明_{settings.size.name}.png"
                        )
                        if standard_path:
                            self.add_photo(standard_path)
                            saved_count += 1
                            self.on_process_log(f"保存标准证件照(透明): {os.path.basename(standard_path)}")
                    except Exception as e:
                        self.on_process_log(f"保存标准证件照(透明)异常: {str(e)}")

                # 保存高清证件照（透明背景）
                if "image_base64_hd" in result and settings.hd:
                    try:
                        hd_path = self.save_base64_image(
                            result["image_base64_hd"],
                            f"证件照_高清_透明_{settings.size.name}.png"
                        )
                        if hd_path:
                            self.add_photo(hd_path)
                            saved_count += 1
                            self.on_process_log(f"保存高清证件照(透明): {os.path.basename(hd_path)}")
                    except Exception as e:
                        self.on_process_log(f"保存高清证件照(透明)异常: {str(e)}")

            return saved_count

        except Exception as e:
            self.on_process_log(f"保存证件照结果异常: {str(e)}")
            return saved_count

    def get_processed_photo_path(self):
        """获取最后处理的照片路径"""
        return self.last_processed_photo_path

    def save_base64_image(self, base64_data: str, filename: str) -> str:
        """保存base64图像到id目录"""
        try:
            # 验证base64数据
            if not base64_data or not isinstance(base64_data, str):
                self.on_process_log(f"无效的base64数据: {type(base64_data)}")
                return ""

            # 修复base64填充问题
            base64_data = fix_base64_padding(base64_data)

            self.on_process_log(f"准备解码base64数据，长度: {len(base64_data)}")

            # 解码base64数据
            image_data = base64.b64decode(base64_data)

            self.on_process_log(f"解码成功，图像数据大小: {len(image_data)} 字节")

            # 验证图像数据
            if len(image_data) < 100:  # 太小的数据可能不是有效图像
                self.on_process_log(f"图像数据太小，可能无效: {len(image_data)} 字节")
                return ""

            # 确定保存目录
            if self.phone_controller:
                # 保存到id目录
                save_dir = self.phone_controller.get_id_photos_directory()
                self.on_process_log(f"保存证件照到: {save_dir}")
            else:
                # 备用：保存到临时目录
                save_dir = tempfile.gettempdir()
                self.on_process_log(f"备用保存到临时目录: {save_dir}")

            # 确保目录存在
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            # 生成带时间戳的文件名
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            name_parts = filename.rsplit('.', 1)
            if len(name_parts) == 2:
                timestamped_filename = f"{name_parts[0]}_{timestamp}.{name_parts[1]}"
            else:
                timestamped_filename = f"{filename}_{timestamp}"

            file_path = os.path.join(save_dir, timestamped_filename)

            # 保存图像
            with open(file_path, 'wb') as f:
                f.write(image_data)

            # 验证文件是否成功保存
            if os.path.exists(file_path) and os.path.getsize(file_path) > 0:
                self.on_process_log(f"证件照保存成功: {file_path}, 大小: {os.path.getsize(file_path)} 字节")
                return file_path
            else:
                self.on_process_log(f"证件照保存失败或文件为空: {file_path}")
                return ""

        except Exception as e:
            self.on_process_log(f"保存证件照失败: {e}")
            import traceback
            self.on_process_log(f"详细错误: {traceback.format_exc()}")
            return ""

    def get_current_photo_data(self) -> bytes:
        """获取当前显示照片的数据"""
        try:
            if not self.photos:
                return None

            # 获取当前选中的照片路径
            current_item = self.photo_list.currentItem()
            if not current_item:
                # 如果没有选中项，使用最后一张照片
                if self.photos:
                    photo_path = self.photos[-1]
                else:
                    return None
            else:
                # 获取选中项对应的照片路径
                index = self.photo_list.row(current_item)
                if 0 <= index < len(self.photos):
                    photo_path = self.photos[index]
                else:
                    return None

            # 读取照片文件数据
            if os.path.exists(photo_path):
                with open(photo_path, 'rb') as f:
                    return f.read()
            else:
                return None

        except Exception as e:
            print(f"获取照片数据失败: {e}")
            return None




class ServerIDPhotoProcessThread(QThread):
    """服务端模式证件照处理线程"""

    progress_updated = pyqtSignal(str)  # 进度更新信号
    processing_finished = pyqtSignal(dict)  # 处理完成信号

    def __init__(self, photo_path, photo_settings, callback=None):
        super().__init__()
        self.photo_path = photo_path
        self.photo_settings = photo_settings
        self.callback = callback
        self.hivision_client = get_hivision_client()

    def run(self):
        """运行证件照处理"""
        try:
            self.progress_updated.emit("开始AI证件照处理...")

            # 从服务端照片设置中提取参数
            size_info = self.photo_settings.get("size", {})
            width = size_info.get("width", 295) if isinstance(size_info, dict) else 295
            height = size_info.get("height", 413) if isinstance(size_info, dict) else 413

            bg_info = self.photo_settings.get("background_color", {})
            if isinstance(bg_info, dict):
                bg_hex = bg_info.get("hex_value", "#438EDB")
            else:
                # 如果bg_info是字符串，直接使用
                bg_hex = bg_info if isinstance(bg_info, str) else "#438EDB"

            matting_info = self.photo_settings.get("matting_model", {})
            matting_model = matting_info.get("model_id", "hivision_modnet") if isinstance(matting_info, dict) else "hivision_modnet"

            face_info = self.photo_settings.get("face_detect_model", {})
            face_model = face_info.get("model_id", "mtcnn") if isinstance(face_info, dict) else "mtcnn"

            dpi = self.photo_settings.get("dpi", 300)
            hd = self.photo_settings.get("hd", False)
            face_alignment = self.photo_settings.get("face_alignment", True)

            # 调用AI处理API
            self.progress_updated.emit("正在调用AI处理API...")

            result = self.hivision_client.create_id_photo(
                image_input=self.photo_path,
                height=height,
                width=width,
                human_matting_model=matting_model,
                face_detect_model=face_model,
                hd=hd,
                dpi=dpi,
                face_alignment=face_alignment
            )

            # 检查API是否返回错误
            if "error" in result:
                error_msg = f"证件照生成失败: {result['error']}"
                self.progress_updated.emit(f"处理失败: {error_msg}")
                self.processing_finished.emit({
                    "success": False,
                    "error": error_msg
                })
                return

            # 检查是否真的生成了图像数据
            has_standard = "image_base64_standard" in result
            has_hd = "image_base64_hd" in result

            if not has_standard and not has_hd:
                error_msg = "API返回成功但未生成任何图像数据，可能是照片中没有检测到人脸或图像质量不佳"
                self.progress_updated.emit(f"处理失败: {error_msg}")
                self.processing_finished.emit({
                    "success": False,
                    "error": error_msg
                })
                return

            if result and (has_standard or has_hd):
                # 添加背景色
                self.progress_updated.emit("正在添加背景色...")
                self.progress_updated.emit(f"AI处理结果状态: {result.get('status')}")

                transparent_image = result.get("image_base64_standard")
                if transparent_image:
                    self.progress_updated.emit(f"获取到透明图像，长度: {len(transparent_image)}")

                    kb_size = self.photo_settings.get("kb_size")
                    if kb_size is None:
                        kb_size = 50  # 默认KB大小

                    self.progress_updated.emit(f"背景参数: 颜色={bg_hex}, KB={kb_size}, 渲染模式={self.photo_settings.get('render_mode', 0)}")

                    bg_result = self.hivision_client.add_background(
                        image_input=transparent_image,
                        color=bg_hex,
                        kb=kb_size,
                        render=self.photo_settings.get("render_mode", 0)
                    )

                    # 检查背景添加是否出错
                    if "error" in bg_result:
                        error_msg = f"背景添加失败: {bg_result['error']}"
                        self.progress_updated.emit(f"处理失败: {error_msg}")
                        self.processing_finished.emit({
                            "success": False,
                            "error": error_msg
                        })
                        return

                    if bg_result and bg_result.get("image_base64"):
                        self.progress_updated.emit("背景添加成功")
                        # 保存处理后的照片
                        final_image = bg_result.get("image_base64")
                        if final_image:
                            self.progress_updated.emit(f"获取到最终图像，长度: {len(final_image)}")
                            processed_path = self.save_processed_photo(final_image)
                            if processed_path:
                                self.progress_updated.emit(f"照片保存成功: {processed_path}")
                                self.processing_finished.emit({
                                    "success": True,
                                    "processed_photo_path": processed_path
                                })
                                return
                            else:
                                self.progress_updated.emit("照片保存失败")
                        else:
                            error_msg = "未获取到最终图像数据"
                            self.progress_updated.emit(error_msg)
                            self.processing_finished.emit({
                                "success": False,
                                "error": error_msg
                            })
                            return
                    else:
                        error_msg = f"背景添加返回异常: {bg_result}"
                        self.progress_updated.emit(error_msg)
                        self.processing_finished.emit({
                            "success": False,
                            "error": error_msg
                        })
                        return
                else:
                    error_msg = "未获取到透明图像数据"
                    self.progress_updated.emit(error_msg)
                    self.processing_finished.emit({
                        "success": False,
                        "error": error_msg
                    })
                    return
            else:
                error_msg = f"AI处理返回异常: {result}"
                self.progress_updated.emit(error_msg)
                self.processing_finished.emit({
                    "success": False,
                    "error": error_msg
                })
                return

        except Exception as e:
            import traceback
            error_detail = traceback.format_exc()
            self.progress_updated.emit(f"处理异常详情: {error_detail}")
            self.processing_finished.emit({
                "success": False,
                "error": f"处理异常: {str(e)}"
            })

    def save_processed_photo(self, image_base64):
        """保存处理后的照片"""
        try:
            import base64
            import tempfile

            # 解码base64图像
            image_data = base64.b64decode(fix_base64_padding(image_base64))

            # 生成临时文件路径
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            temp_path = os.path.join(tempfile.gettempdir(), f"id_photo_{timestamp}.jpg")

            # 保存文件
            with open(temp_path, 'wb') as f:
                f.write(image_data)

            return temp_path

        except Exception as e:
            self.progress_updated.emit(f"保存照片失败: {e}")
            return None
