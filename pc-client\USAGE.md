# 快速使用指南

## 🚀 快速开始

### 1. 安装程序
```bash
# 方法1: 运行安装脚本
python install.py

# 方法2: 手动安装依赖
pip install -r requirements.txt
```

### 2. 启动程序
```bash
# Windows用户（推荐）
双击 start.bat

# 或者使用命令行
python run.py
```

### 3. 连接手机
1. 确保手机和电脑在同一WiFi网络
2. 启动手机上的"学生拍照"应用
3. 在手机应用中查看IP地址（例如：*************）
4. 在电脑客户端输入手机IP地址
5. 点击"连接手机"

### 4. 开始拍照
- 点击"📸 拍照"进行单次拍照
- 点击"📷 连续拍照"进行连续拍摄
- 在右侧查看器中查看照片
- 选择照片后可以保存到本地

## 🔧 测试功能

如果需要测试客户端功能（无需真实手机）：

```bash
# 启动测试工具
python test_client.py

# 选择选项2启动模拟服务器
# 然后在客户端连接到 127.0.0.1:8080
```

## ❓ 常见问题

**Q: 连接不上手机怎么办？**
A: 
1. 检查手机和电脑是否在同一WiFi
2. 确认手机应用正在运行
3. 检查IP地址是否正确
4. 尝试关闭防火墙

**Q: 照片无法显示？**
A: 
1. 确认Pillow库已正确安装
2. 检查照片文件是否存在
3. 重新拍摄照片

**Q: 程序无法启动？**
A: 
1. 检查Python版本（需要3.7+）
2. 确认PyQt5已正确安装
3. 运行 `python install.py` 重新安装

## 📞 技术支持

如遇到问题，请查看操作日志中的错误信息，或联系技术支持。
