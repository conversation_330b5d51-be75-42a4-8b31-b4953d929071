<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">

    <!-- 顶部标题栏 -->
    <LinearLayout
        android:id="@+id/title_bar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="StudentFoto"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="#333333" />

        <Button
            android:id="@+id/menu_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="菜单"
            android:textSize="14sp"
            android:backgroundTint="#2196F3"
            android:textColor="#ffffff" />

    </LinearLayout>

    <!-- 任务信息显示区域 -->
    <LinearLayout
        android:id="@+id/task_info_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="16dp"
        android:orientation="vertical"
        android:background="@drawable/task_info_background"
        android:padding="12dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title_bar">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="当前任务"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="#666666"
            android:gravity="center" />

        <TextView
            android:id="@+id/task_info_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="姓名: 张三\n身份证号: 123456789012345678"
            android:textSize="16sp"
            android:textColor="#333333"
            android:gravity="center"
            android:lineSpacingExtra="4dp" />

    </LinearLayout>

    <TextView
        android:id="@+id/connection_status_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:text="📱 等待PC连接..."
        android:textSize="14sp"
        android:textColor="#FF9800"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/task_info_layout" />

    <androidx.camera.view.PreviewView
        android:id="@+id/viewFinder"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        app:layout_constraintBottom_toTopOf="@+id/capture_button"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/connection_status_text" />

    <!-- 处理后照片预览 -->
    <ImageView
        android:id="@+id/processed_photo_preview"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        android:scaleType="fitCenter"
        android:background="#000000"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/photo_action_buttons"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/connection_status_text" />

    <!-- 照片操作按钮 -->
    <LinearLayout
        android:id="@+id/photo_action_buttons"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:layout_marginEnd="32dp"
        android:layout_marginBottom="32dp"
        android:orientation="horizontal"
        android:gravity="center"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <Button
            android:id="@+id/retake_photo_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="16dp"
            android:text="重新拍照"
            android:backgroundTint="@android:color/holo_orange_dark"
            android:textColor="@android:color/white" />

        <Button
            android:id="@+id/confirm_photo_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="确认上传"
            android:backgroundTint="@android:color/holo_green_dark"
            android:textColor="@android:color/white" />

    </LinearLayout>

    <Button
        android:id="@+id/capture_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        android:text="拍照"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
