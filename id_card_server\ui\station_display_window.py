#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机位号显示窗口
用于在多显示器环境下显示分配的机位号
"""

from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel, QFrame
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QPalette, QColor
import logging

logger = logging.getLogger(__name__)

class StationDisplayWindow(QWidget):
    """机位号显示窗口"""

    # 信号定义
    window_closed = pyqtSignal()  # 窗口关闭信号

    def __init__(self):
        super().__init__()
        self.current_station = None
        self.init_ui()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("机位号显示")
        self.setWindowFlags(Qt.Window | Qt.WindowStaysOnTopHint)
        self.resize(400, 300)
        
        # 设置窗口样式
        self.setStyleSheet("""
            QWidget {
                background-color: #2c3e50;
                color: white;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # 标题
        title_label = QLabel("请到指定机位排队")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Microsoft YaHei", 24, QFont.Bold))
        title_label.setStyleSheet("color: #ecf0f1; margin-bottom: 20px;")
        layout.addWidget(title_label)
        
        # 机位号显示区域
        self.station_frame = QFrame()
        self.station_frame.setFrameStyle(QFrame.Box | QFrame.Raised)
        self.station_frame.setLineWidth(3)
        self.station_frame.setStyleSheet("""
            QFrame {
                background-color: #34495e;
                border: 3px solid #3498db;
                border-radius: 15px;
                padding: 20px;
            }
        """)
        
        station_layout = QVBoxLayout(self.station_frame)
        
        # 机位号标签
        self.station_label = QLabel("等待分配...")
        self.station_label.setAlignment(Qt.AlignCenter)
        self.station_label.setFont(QFont("Microsoft YaHei", 72, QFont.Bold))
        self.station_label.setStyleSheet("""
            color: #e74c3c;
            background-color: transparent;
            border: none;
            padding: 20px;
        """)
        station_layout.addWidget(self.station_label)
        
        layout.addWidget(self.station_frame)
        
        # 说明文字
        info_label = QLabel("请记住您的机位号，前往对应PC端拍照")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setFont(QFont("Microsoft YaHei", 16))
        info_label.setStyleSheet("color: #bdc3c7; margin-top: 20px;")
        layout.addWidget(info_label)
        
        # 闪烁定时器
        self.blink_timer = QTimer()
        self.blink_timer.timeout.connect(self.blink_station)
        self.blink_state = True
        
    def show_station(self, station_number: int, name: str = ""):
        """显示机位号"""
        try:
            self.current_station = station_number
            
            # 更新显示文本
            if name:
                display_text = f"{station_number}号机位\n{name}"
                self.station_label.setFont(QFont("Microsoft YaHei", 48, QFont.Bold))
            else:
                display_text = f"{station_number}号机位"
                self.station_label.setFont(QFont("Microsoft YaHei", 72, QFont.Bold))
            
            self.station_label.setText(display_text)
            
            # 开始闪烁效果
            self.start_blinking()
            
            # 显示窗口
            self.show()
            self.raise_()
            self.activateWindow()
            
            logger.info(f"显示机位号: {station_number}号 ({name})")
            
        except Exception as e:
            logger.error(f"显示机位号失败: {e}")
    
    def start_blinking(self):
        """开始闪烁效果"""
        self.blink_timer.start(800)  # 每800毫秒闪烁一次
        
    def stop_blinking(self):
        """停止闪烁效果"""
        self.blink_timer.stop()
        # 恢复正常颜色
        self.station_label.setStyleSheet("""
            color: #e74c3c;
            background-color: transparent;
            border: none;
            padding: 20px;
        """)
        
    def blink_station(self):
        """闪烁机位号"""
        if self.blink_state:
            # 高亮状态
            self.station_label.setStyleSheet("""
                color: #f1c40f;
                background-color: transparent;
                border: none;
                padding: 20px;
            """)
        else:
            # 正常状态
            self.station_label.setStyleSheet("""
                color: #e74c3c;
                background-color: transparent;
                border: none;
                padding: 20px;
            """)
        
        self.blink_state = not self.blink_state
    
    def clear_display(self):
        """清空显示"""
        self.stop_blinking()
        self.station_label.setText("等待分配...")
        self.station_label.setFont(QFont("Microsoft YaHei", 72, QFont.Bold))
        self.current_station = None
        
    def closeEvent(self, event):
        """关闭事件"""
        self.stop_blinking()
        self.window_closed.emit()  # 发出窗口关闭信号
        event.accept()
        
    def keyPressEvent(self, event):
        """按键事件"""
        if event.key() == Qt.Key_Escape:
            self.hide()
        else:
            super().keyPressEvent(event)
