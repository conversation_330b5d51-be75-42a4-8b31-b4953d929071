#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
身份证读卡器32位桥接程序
用于在64位Python环境中调用32位DLL
"""

import ctypes
from ctypes import c_int, c_char_p, POINTER, byref, create_string_buffer
import os
import sys
import json
import socket
import threading
import time

class IDCardBridge:
    """32位DLL桥接器"""
    
    def __init__(self):
        self.dll = None
        self.is_connected = False
        self.load_dll()
    
    def load_dll(self):
        """加载DLL库"""
        try:
            dll_path = os.path.join("server", "Termb.dll")
            if not os.path.exists(dll_path):
                raise FileNotFoundError(f"DLL文件不存在: {dll_path}")
            
            self.dll = ctypes.WinDLL(dll_path)
            
            # 定义函数原型
            self.dll.CVR_InitComm.argtypes = [c_int]
            self.dll.CVR_InitComm.restype = c_int
            
            self.dll.CVR_Authenticate.argtypes = []
            self.dll.CVR_Authenticate.restype = c_int
            
            self.dll.CVR_Read_FPContent.argtypes = []
            self.dll.CVR_Read_FPContent.restype = c_int
            
            self.dll.CVR_CloseComm.argtypes = []
            self.dll.CVR_CloseComm.restype = c_int
            
            self.dll.GetPeopleName.argtypes = [c_char_p, POINTER(c_int)]
            self.dll.GetPeopleName.restype = c_int
            
            self.dll.GetPeopleSex.argtypes = [c_char_p, POINTER(c_int)]
            self.dll.GetPeopleSex.restype = c_int
            
            self.dll.GetPeopleBirthday.argtypes = [c_char_p, POINTER(c_int)]
            self.dll.GetPeopleBirthday.restype = c_int
            
            self.dll.GetPeopleIDCode.argtypes = [c_char_p, POINTER(c_int)]
            self.dll.GetPeopleIDCode.restype = c_int
            
            print("DLL加载成功")
            
        except Exception as e:
            print(f"加载DLL失败: {e}")
            raise e
    
    def connect(self, port=1001):
        """连接读卡器"""
        try:
            result = self.dll.CVR_InitComm(port)
            if result == 1:
                self.is_connected = True
                return True
            return False
        except Exception as e:
            print(f"连接读卡器异常: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        try:
            if self.is_connected:
                result = self.dll.CVR_CloseComm()
                self.is_connected = False
                return result == 1
            return True
        except Exception as e:
            print(f"断开连接异常: {e}")
            return False
    
    def authenticate_card(self):
        """卡认证"""
        if not self.is_connected:
            return False
        
        try:
            result = self.dll.CVR_Authenticate()
            return result == 1
        except Exception as e:
            print(f"卡认证异常: {e}")
            return False
    
    def read_card_info(self):
        """读取身份证信息"""
        if not self.authenticate_card():
            return None
        
        try:
            # 读取卡片内容
            result = self.dll.CVR_Read_FPContent()
            if result != 1:
                return None
            
            # 获取各项信息
            card_info = {
                'name': self._get_name(),
                'gender': self._get_gender(),
                'birth_date': self._get_birth_date(),
                'id_number': self._get_id_number()
            }
            
            # 验证必要字段
            if not all([card_info['name'], card_info['gender'], 
                       card_info['birth_date'], card_info['id_number']]):
                return None
            
            return card_info
            
        except Exception as e:
            print(f"读取身份证信息异常: {e}")
            return None
    
    def _get_name(self):
        """获取姓名"""
        try:
            buffer = create_string_buffer(100)
            length = c_int(100)
            result = self.dll.GetPeopleName(buffer, byref(length))
            if result == 1:
                return buffer.value.decode('gbk', errors='ignore').strip()
            return ""
        except Exception as e:
            print(f"获取姓名失败: {e}")
            return ""
    
    def _get_gender(self):
        """获取性别"""
        try:
            buffer = create_string_buffer(10)
            length = c_int(10)
            result = self.dll.GetPeopleSex(buffer, byref(length))
            if result == 1:
                return buffer.value.decode('gbk', errors='ignore').strip()
            return ""
        except Exception as e:
            print(f"获取性别失败: {e}")
            return ""
    
    def _get_birth_date(self):
        """获取出生日期"""
        try:
            buffer = create_string_buffer(20)
            length = c_int(20)
            result = self.dll.GetPeopleBirthday(buffer, byref(length))
            if result == 1:
                return buffer.value.decode('gbk', errors='ignore').strip()
            return ""
        except Exception as e:
            print(f"获取出生日期失败: {e}")
            return ""
    
    def _get_id_number(self):
        """获取身份证号码"""
        try:
            buffer = create_string_buffer(50)
            length = c_int(50)
            result = self.dll.GetPeopleIDCode(buffer, byref(length))
            if result == 1:
                return buffer.value.decode('gbk', errors='ignore').strip()
            return ""
        except Exception as e:
            print(f"获取身份证号码失败: {e}")
            return ""

def start_bridge_server(port=9091):
    """启动桥接服务器"""
    bridge = IDCardBridge()
    
    server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
    server_socket.bind(('localhost', port))
    server_socket.listen(1)
    
    print(f"桥接服务器启动，监听端口: {port}")
    
    while True:
        try:
            client_socket, address = server_socket.accept()
            print(f"客户端连接: {address}")
            
            while True:
                try:
                    data = client_socket.recv(1024).decode('utf-8')
                    if not data:
                        break
                    
                    request = json.loads(data)
                    command = request.get('command')
                    
                    if command == 'connect':
                        port = request.get('port', 1001)
                        result = bridge.connect(port)
                        response = {'success': result}
                    elif command == 'disconnect':
                        result = bridge.disconnect()
                        response = {'success': result}
                    elif command == 'read_card':
                        card_info = bridge.read_card_info()
                        response = {'success': card_info is not None, 'data': card_info}
                    else:
                        response = {'success': False, 'error': 'Unknown command'}
                    
                    client_socket.send(json.dumps(response).encode('utf-8'))
                    
                except json.JSONDecodeError:
                    error_response = {'success': False, 'error': 'Invalid JSON'}
                    client_socket.send(json.dumps(error_response).encode('utf-8'))
                except Exception as e:
                    error_response = {'success': False, 'error': str(e)}
                    client_socket.send(json.dumps(error_response).encode('utf-8'))
                    
        except Exception as e:
            print(f"服务器错误: {e}")
        finally:
            try:
                client_socket.close()
            except:
                pass

if __name__ == "__main__":
    start_bridge_server()
