"""
设备选择对话框
显示发现的设备列表，允许用户选择要连接的设备
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QListWidget, 
                             QListWidgetItem, QPushButton, QLabel, QLineEdit,
                             QGroupBox, QMessageBox, QProgressBar)
from PyQt5.QtCore import QTimer, pyqtSignal, Qt
from PyQt5.QtGui import QFont
import logging
from typing import Optional
from core.device_discovery import DeviceDiscoveryService, DeviceInfo

logger = logging.getLogger(__name__)

class DeviceListItem(QListWidgetItem):
    """设备列表项"""
    def __init__(self, device: DeviceInfo):
        super().__init__()
        self.device = device
        self.update_display()
    
    def update_display(self):
        """更新显示内容"""
        self.setText(f"{self.device.device_name}\n{self.device.device_ip}:{self.device.tcp_port}")
        
        # 设置字体
        font = QFont()
        font.setPointSize(10)
        self.setFont(font)

class DeviceSelectorDialog(QDialog):
    """设备选择对话框"""

    device_connected = pyqtSignal(str, int)  # ip, port - 直接连接设备
    
    def __init__(self, parent=None, phone_controller=None):
        super().__init__(parent)
        self.discovery_service = DeviceDiscoveryService()
        self.selected_device: Optional[DeviceInfo] = None
        self.phone_controller = phone_controller

        self.init_ui()
        self.setup_discovery_service()

        # 定时器用于更新界面
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_device_list)
        self.update_timer.start(1000)  # 每秒更新一次
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("连接设备")
        self.setFixedSize(500, 600)

        layout = QVBoxLayout()

        # 标题
        title_label = QLabel("选择要连接的设备")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 设备发现状态
        self.discovery_status_label = QLabel("正在搜索设备...")
        layout.addWidget(self.discovery_status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 0)  # 无限进度条
        layout.addWidget(self.progress_bar)
        
        # 设备列表
        device_group = QGroupBox("发现的设备")
        device_layout = QVBoxLayout()
        
        self.device_list = QListWidget()
        self.device_list.itemClicked.connect(self.on_device_selected)
        self.device_list.itemDoubleClicked.connect(self.on_device_double_clicked)
        device_layout.addWidget(self.device_list)
        
        # 刷新按钮
        refresh_button = QPushButton("🔄 刷新")
        refresh_button.clicked.connect(self.refresh_devices)
        device_layout.addWidget(refresh_button)
        
        device_group.setLayout(device_layout)
        layout.addWidget(device_group)
        
        # 手动输入组
        manual_group = QGroupBox("手动输入")
        manual_layout = QVBoxLayout()
        
        # IP输入
        ip_layout = QHBoxLayout()
        ip_layout.addWidget(QLabel("IP地址:"))
        self.ip_input = QLineEdit()
        self.ip_input.setPlaceholderText("例如: *************")
        self.ip_input.textChanged.connect(self.on_manual_input_changed)
        ip_layout.addWidget(self.ip_input)
        manual_layout.addLayout(ip_layout)

        # 端口输入
        port_layout = QHBoxLayout()
        port_layout.addWidget(QLabel("端口:"))
        self.port_input = QLineEdit()
        self.port_input.setText("8080")
        self.port_input.setPlaceholderText("8080")
        self.port_input.textChanged.connect(self.on_manual_input_changed)
        port_layout.addWidget(self.port_input)
        manual_layout.addLayout(port_layout)
        
        manual_group.setLayout(manual_layout)
        layout.addWidget(manual_group)
        
        # 按钮
        button_layout = QHBoxLayout()

        self.connect_button = QPushButton("连接设备")
        self.connect_button.clicked.connect(self.connect_to_device)
        self.connect_button.setEnabled(False)
        self.connect_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        button_layout.addWidget(self.connect_button)

        cancel_button = QPushButton("取消")
        cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(cancel_button)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def setup_discovery_service(self):
        """设置设备发现服务"""
        self.discovery_service.on_device_found = self.on_device_found
        self.discovery_service.on_device_lost = self.on_device_lost
        self.discovery_service.on_device_updated = self.on_device_updated
        
        try:
            self.discovery_service.start()
            self.discovery_status_label.setText("正在搜索设备...")
        except Exception as e:
            logger.error(f"Failed to start discovery service: {e}")
            self.discovery_status_label.setText(f"搜索失败: {e}")
            self.progress_bar.setVisible(False)
    
    def on_device_found(self, device: DeviceInfo):
        """设备发现回调"""
        logger.info(f"Device found: {device}")
        # 界面更新在update_device_list中处理
    
    def on_device_lost(self, device: DeviceInfo):
        """设备丢失回调"""
        logger.info(f"Device lost: {device}")
        # 界面更新在update_device_list中处理
    
    def on_device_updated(self, device: DeviceInfo):
        """设备更新回调"""
        logger.debug(f"Device updated: {device}")
        # 界面更新在update_device_list中处理
    
    def update_device_list(self):
        """更新设备列表显示"""
        devices = self.discovery_service.get_devices()
        
        # 更新状态标签
        if devices:
            self.discovery_status_label.setText(f"发现 {len(devices)} 个设备")
            self.progress_bar.setVisible(False)
        else:
            self.discovery_status_label.setText("正在搜索设备...")
            self.progress_bar.setVisible(True)
        
        # 清空列表
        self.device_list.clear()
        
        # 添加设备
        for device in devices:
            item = DeviceListItem(device)
            self.device_list.addItem(item)
    
    def on_device_selected(self, item: QListWidgetItem):
        """设备选择事件"""
        if isinstance(item, DeviceListItem):
            self.selected_device = item.device
            self.connect_button.setEnabled(True)

            # 更新手动输入框（临时断开信号连接避免触发验证）
            self.ip_input.textChanged.disconnect()
            self.port_input.textChanged.disconnect()

            self.ip_input.setText(self.selected_device.device_ip)
            self.port_input.setText(str(self.selected_device.tcp_port))

            # 重新连接信号
            self.ip_input.textChanged.connect(self.on_manual_input_changed)
            self.port_input.textChanged.connect(self.on_manual_input_changed)
    
    def on_device_double_clicked(self, item: QListWidgetItem):
        """设备双击事件"""
        self.on_device_selected(item)
        self.connect_to_device()
    
    def refresh_devices(self):
        """刷新设备列表"""
        self.discovery_service.clear_devices()
        self.device_list.clear()
        self.selected_device = None
        self.connect_button.setEnabled(False)
        self.discovery_status_label.setText("正在搜索设备...")
        self.progress_bar.setVisible(True)

    def on_manual_input_changed(self):
        """手动输入变化事件"""
        ip_text = self.ip_input.text().strip()
        port_text = self.port_input.text().strip()

        # 检查IP地址是否有效
        if ip_text:
            # 简单的IP地址格式验证
            ip_parts = ip_text.split('.')
            is_valid_ip = (
                len(ip_parts) == 4 and
                all(part.isdigit() and 0 <= int(part) <= 255 for part in ip_parts)
            )

            # 检查端口是否有效
            is_valid_port = True
            if port_text:
                try:
                    port = int(port_text)
                    is_valid_port = 1 <= port <= 65535
                except ValueError:
                    is_valid_port = False

            # 如果IP有效且端口有效（或为空，使用默认端口），则启用连接按钮
            if is_valid_ip and is_valid_port:
                self.connect_button.setEnabled(True)
                # 清除设备列表选择，因为现在使用手动输入
                self.device_list.clearSelection()
                self.selected_device = None
            else:
                self.connect_button.setEnabled(False)
        else:
            # IP地址为空时，检查是否有选中的设备
            if self.selected_device:
                self.connect_button.setEnabled(True)
            else:
                self.connect_button.setEnabled(False)
    
    def connect_to_device(self):
        """连接到设备"""
        try:
            # 获取IP和端口
            if self.selected_device:
                ip = self.selected_device.device_ip
                port = self.selected_device.tcp_port
                device_name = f"{self.selected_device.device_name} ({ip}:{port})"
            else:
                ip = self.ip_input.text().strip()
                port_text = self.port_input.text().strip()

                if not ip:
                    QMessageBox.warning(self, "错误", "请输入IP地址")
                    return

                try:
                    port = int(port_text) if port_text else 8080
                except ValueError:
                    QMessageBox.warning(self, "错误", "端口必须是数字")
                    return

                device_name = f"手动输入 ({ip}:{port})"

            # 确认连接
            reply = QMessageBox.question(
                self,
                "确认连接",
                f"确定要连接到设备：\n{device_name}\n\n连接可能需要几秒钟时间。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if reply == QMessageBox.Yes:
                # 发射连接信号，直接连接设备
                self.device_connected.emit(ip, port)
                self.accept()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"连接失败: {e}")
    
    def closeEvent(self, event):
        """关闭事件"""
        self.cleanup()
        super().closeEvent(event)
    
    def cleanup(self):
        """清理资源"""
        if self.update_timer:
            self.update_timer.stop()
        
        if self.discovery_service:
            self.discovery_service.stop()
    
    def reject(self):
        """取消对话框"""
        self.cleanup()
        super().reject()
    
    def accept(self):
        """接受对话框"""
        self.cleanup()
        super().accept()
