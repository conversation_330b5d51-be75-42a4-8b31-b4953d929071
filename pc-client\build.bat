@echo off
chcp 65001 >nul
echo ================================================
echo StudentFoto PC客户端打包工具
echo ================================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo 请先安装Python 3.7或更高版本
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

echo 正在安装依赖包...
python -m pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ 依赖包安装失败
    pause
    exit /b 1
)

echo ✅ 依赖包安装完成
echo.

echo 开始打包程序...
python build_exe.py
if errorlevel 1 (
    echo ❌ 打包失败
    pause
    exit /b 1
)

echo.
echo ================================================
echo 🎉 打包完成！
echo 📦 可执行文件位于 release/ 目录
echo ================================================
echo.

echo 是否打开release目录？(Y/N)
set /p choice=
if /i "%choice%"=="Y" (
    explorer release
)

pause
