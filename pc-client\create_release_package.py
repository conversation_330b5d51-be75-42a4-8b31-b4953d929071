#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建完整的发布包
"""

import os
import shutil
import zipfile
import datetime
from pathlib import Path

def create_release_package():
    """创建完整的发布包"""
    print("正在创建发布包...")
    
    # 检查必要文件
    exe_file = Path("release/StudentFoto-PC客户端.exe")
    if not exe_file.exists():
        print("❌ 可执行文件不存在，请先运行打包脚本")
        return False
    
    # 创建发布包目录
    package_dir = Path("StudentFoto-PC客户端-v1.0")
    if package_dir.exists():
        shutil.rmtree(package_dir)
    package_dir.mkdir()
    
    # 复制可执行文件
    shutil.copy2(exe_file, package_dir)
    
    # 创建详细的使用说明
    readme_content = """# StudentFoto PC客户端 v1.0

## 软件介绍

StudentFoto是一个学生拍照系统，包含Android手机端和PC客户端。本程序是PC客户端，用于连接Android设备进行远程拍照。

## 安装说明

### 系统要求
- Windows 7 或更高版本（推荐Windows 10/11）
- 至少100MB可用磁盘空间
- 网络连接（与Android设备在同一局域网）

### 安装步骤
1. 将整个文件夹复制到您希望安装的位置
2. 双击"StudentFoto-PC客户端.exe"即可运行
3. 无需额外安装任何软件或依赖

## 使用说明

### 首次使用
1. 确保Android设备已安装StudentFoto应用并正在运行
2. 确保PC和Android设备连接到同一WiFi网络
3. 启动PC客户端程序
4. 点击"🔍 发现并连接设备"按钮

### 连接设备
1. 程序会自动搜索网络中的Android设备
2. 在设备列表中选择要连接的设备
3. 点击"连接设备"按钮
4. 连接成功后界面会显示"已连接"状态

### 拍照操作
1. 连接成功后，"📸 拍照"按钮会变为可用状态
2. 点击拍照按钮，Android设备会自动拍照
3. 拍摄的照片会自动传输到PC并显示在右侧预览区域
4. 照片会自动保存到系统临时目录

### 断开连接
1. 点击"断开连接"按钮
2. 程序会断开与Android设备的连接
3. 可以重新搜索和连接其他设备

## 注意事项

### 首次运行
- 首次启动可能需要10-30秒时间，请耐心等待
- Windows Defender可能会显示安全警告，请选择"仍要运行"或"允许"
- 如果程序被防火墙阻止，请允许程序访问网络

### 网络设置
- 确保PC和Android设备在同一WiFi网络下
- 如果使用企业网络，可能需要IT管理员协助配置
- 程序使用8080端口进行通信，确保该端口未被占用

### 故障排除
- 如果无法发现设备，请检查网络连接
- 如果连接失败，请重启Android应用
- 如果程序无响应，请关闭后重新启动

## 技术支持

### 常见问题
1. **程序启动慢**：首次启动需要加载所有组件，属于正常现象
2. **找不到设备**：检查网络连接，确保设备在同一网络
3. **连接失败**：重启Android应用，确保应用正在运行
4. **照片传输失败**：检查网络稳定性，重新连接设备

### 联系方式
如有问题，请联系技术支持。

## 版本信息

- 版本：1.0
- 构建时间：{build_time}
- 支持系统：Windows 7/8/10/11
- 文件大小：约58MB

## 更新日志

### v1.0 (2025-06-23)
- 首个正式版本
- 支持设备自动发现
- 支持远程拍照
- 支持照片预览和保存
- 优化用户界面
- 修复断开连接问题

## 许可协议

本软件仅供学习和研究使用。
"""
    
    build_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    with open(package_dir / "使用说明.txt", 'w', encoding='utf-8') as f:
        f.write(readme_content.format(build_time=build_time))
    
    # 创建快速启动说明
    quick_start = """快速启动指南

1. 双击"StudentFoto-PC客户端.exe"启动程序
2. 点击"发现并连接设备"
3. 选择Android设备并连接
4. 点击"拍照"按钮开始使用

注意：首次启动可能需要30秒，请耐心等待。
"""
    
    with open(package_dir / "快速启动.txt", 'w', encoding='utf-8') as f:
        f.write(quick_start)
    
    # 创建系统要求说明
    requirements = """系统要求

最低要求：
- Windows 7 SP1 或更高版本
- 1GB RAM
- 100MB 可用磁盘空间
- 网络连接

推荐配置：
- Windows 10 或 Windows 11
- 2GB RAM 或更多
- 稳定的WiFi网络连接

必需组件：
- Microsoft Visual C++ Redistributable（通常已预装）
- 如果程序无法启动，请下载安装：
  https://aka.ms/vs/17/release/vc_redist.x64.exe
"""
    
    with open(package_dir / "系统要求.txt", 'w', encoding='utf-8') as f:
        f.write(requirements)
    
    # 获取文件大小信息
    exe_size = exe_file.stat().st_size
    size_mb = exe_size / (1024 * 1024)
    
    print(f"✅ 发布包创建完成: {package_dir.absolute()}")
    print(f"📁 包含文件:")
    print(f"   - StudentFoto-PC客户端.exe ({size_mb:.1f}MB)")
    print(f"   - 使用说明.txt")
    print(f"   - 快速启动.txt")
    print(f"   - 系统要求.txt")
    
    return True

def create_zip_package():
    """创建ZIP压缩包"""
    package_dir = Path("StudentFoto-PC客户端-v1.0")
    if not package_dir.exists():
        print("❌ 发布包目录不存在")
        return False
    
    zip_name = "StudentFoto-PC客户端-v1.0.zip"
    
    print(f"正在创建ZIP压缩包: {zip_name}")
    
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for file_path in package_dir.rglob('*'):
            if file_path.is_file():
                arcname = file_path.relative_to(package_dir.parent)
                zipf.write(file_path, arcname)
    
    zip_size = Path(zip_name).stat().st_size / (1024 * 1024)
    print(f"✅ ZIP压缩包创建完成: {zip_name} ({zip_size:.1f}MB)")
    
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("StudentFoto PC客户端发布包创建工具")
    print("=" * 50)
    
    # 创建发布包
    if not create_release_package():
        return False
    
    # 创建ZIP压缩包
    if not create_zip_package():
        return False
    
    print("\n" + "=" * 50)
    print("🎉 发布包创建完成！")
    print("📦 可以将ZIP文件分发给用户")
    print("=" * 50)
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        exit(1)
