package com.example.studentfoto

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix
import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.hardware.SensorManager
import android.media.ExifInterface
import android.net.Uri
import android.net.wifi.WifiInfo
import android.net.wifi.WifiManager
import android.os.Bundle
import android.provider.MediaStore
import android.util.Log
import android.view.WindowManager
import androidx.appcompat.app.AppCompatActivity
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageCapture
import androidx.camera.core.ImageCaptureException
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import android.widget.Button
import android.widget.EditText
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import android.view.View
import android.widget.Toast
import android.content.SharedPreferences
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import kotlin.properties.Delegates
import java.io.IOException
import java.io.ByteArrayOutputStream
import java.net.ServerSocket
import java.net.Socket
import android.content.ContentValues
import androidx.core.os.bundleOf
import com.example.studentfoto.R  // 导入 R 类以解决 layout 和 id 的引用问题

class MainActivity : AppCompatActivity(), SensorEventListener {

    private lateinit var cameraExecutor: ExecutorService
    private var imageCapture: ImageCapture? = null
    private var isCameraBound = false  // 相机绑定状态标志
    private var _ipAddress by Delegates.notNull<String>()
    private val ipAddress: String get() = _ipAddress
    private lateinit var serverSocket: ServerSocket
    private var clientSocket: Socket? = null
    private var isServerRunning = false
    private var connectedPcInfo: String? = null
    private var isBroadcasting = false

    // 任务相关
    private var currentTask: Map<String, String>? = null
    private var waitingForProcessedPhoto = false
    private var processedPhotoData: ByteArray? = null
    private var processedPhotoBuffer = mutableListOf<Byte>()
    private var expectedProcessedPhotoSize = 0

    // 重力感应相关
    private lateinit var sensorManager: SensorManager
    private var accelerometer: Sensor? = null
    private var currentOrientation = 0 // 0=竖屏, 90=左横屏, 180=倒立, 270=右横屏

    // 广播服务相关
    private lateinit var broadcastService: BroadcastService
    private lateinit var sharedPreferences: SharedPreferences
    private var deviceName: String = "Android设备"

    // 相机重试相关
    private var cameraRetryCount = 0
    private val maxCameraRetries = 3

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        // 保持屏幕常亮，防止息屏
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        Log.d("MainActivity", "Screen keep on enabled - 屏幕保持常亮已启用")

        // 初始化执行器服务
        cameraExecutor = Executors.newSingleThreadExecutor()

        // 获取手机IP地址
        _ipAddress = getIPAddress()
        Log.d("MainActivity", "Device IP Address: $ipAddress")

        // 初始化连接状态显示
        updateConnectionStatus("等待PC连接...")

        // 初始化SharedPreferences
        sharedPreferences = getSharedPreferences("StudentFotoPrefs", Context.MODE_PRIVATE)

        // 加载保存的设备名
        deviceName = sharedPreferences.getString("device_name", "Android设备") ?: "Android设备"

        // 初始化广播服务
        broadcastService = BroadcastService(this)

        // 初始化重力感应
        initSensor()

        // 请求相机权限
        if (allPermissionsGranted()) {
            startCamera()
        } else {
            ActivityCompat.requestPermissions(
                this, REQUIRED_PERMISSIONS, REQUEST_CODE_PERMISSIONS
            )
        }

        // 设置拍照按钮点击事件
        findViewById<Button>(R.id.capture_button).setOnClickListener { takePhoto() }

        // 设置菜单按钮点击事件
        findViewById<Button>(R.id.menu_button).setOnClickListener { showMenuDialog() }

        // 启动服务器监听电脑端控制
        startServer()

        // 启动广播服务
        startBroadcastService()
    }

    private fun startCamera() {
        Log.d("MainActivity", "Starting camera, retry count: $cameraRetryCount")

        // 检查是否已经在绑定过程中
        if (isCameraBound) {
            Log.d("MainActivity", "Camera already bound, skipping")
            return
        }

        val cameraProviderFuture = ProcessCameraProvider.getInstance(this@MainActivity)

        cameraProviderFuture.addListener({
            try {
                val cameraProvider: ProcessCameraProvider = cameraProviderFuture.get()

                // 确保在主线程中执行UI相关操作
                runOnUiThread {
                    bindCamera(cameraProvider)
                }

            } catch (exc: Exception) {
                Log.e("MainActivity", "Failed to get camera provider", exc)
                handleCameraError(exc)
            }
        }, ContextCompat.getMainExecutor(this@MainActivity))
    }

    private fun bindCamera(cameraProvider: ProcessCameraProvider) {
        try {
            Log.d("MainActivity", "Binding camera to lifecycle")

            // 确保PreviewView已经准备好
            val previewView = findViewById<PreviewView>(R.id.viewFinder)
            if (previewView == null) {
                Log.e("MainActivity", "PreviewView not found")
                handleCameraError(Exception("PreviewView not found"))
                return
            }

            val preview = Preview.Builder()
                .build()
                .also {
                    it.setSurfaceProvider(previewView.surfaceProvider)
                }

            imageCapture = ImageCapture.Builder()
                .setCaptureMode(ImageCapture.CAPTURE_MODE_MINIMIZE_LATENCY)
                .build()

            val cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA

            // 解绑所有用例
            cameraProvider.unbindAll()
            isCameraBound = false

            // 等待一小段时间确保解绑完成
            Thread.sleep(100)

            // 绑定用例到生命周期
            cameraProvider.bindToLifecycle(
                this@MainActivity, cameraSelector, preview, imageCapture
            )

            isCameraBound = true
            cameraRetryCount = 0  // 重置重试计数
            Log.d("MainActivity", "Camera bound successfully")

            // 更新相机方向
            updateCameraOrientation()

        } catch (exc: Exception) {
            Log.e("MainActivity", "Camera binding failed", exc)
            handleCameraError(exc)
        }
    }

    private fun handleCameraError(exception: Exception) {
        isCameraBound = false
        imageCapture = null

        if (cameraRetryCount < maxCameraRetries) {
            cameraRetryCount++
            Log.w("MainActivity", "Camera binding failed, retrying ($cameraRetryCount/$maxCameraRetries)")

            // 延迟重试
            Thread {
                Thread.sleep(1000)
                runOnUiThread {
                    if (allPermissionsGranted()) {
                        startCamera()
                    }
                }
            }.start()
        } else {
            Log.e("MainActivity", "Camera binding failed after $maxCameraRetries retries")
            runOnUiThread {
                Toast.makeText(this, "相机初始化失败，请重启应用", Toast.LENGTH_LONG).show()
            }
        }
    }

    private fun takePhoto() {
        Log.d("MainActivity", "Take photo requested")

        // 允许在没有任务时也能拍照（用于测试或本地模式）
        if (currentTask == null) {
            Log.d("MainActivity", "没有当前任务，但允许拍照")
        }

        // 检查相机是否已绑定且ImageCapture可用
        val imageCapture = imageCapture
        if (imageCapture == null || !isCameraBound) {
            Log.e("MainActivity", "Camera not ready - imageCapture: $imageCapture, isCameraBound: $isCameraBound")

            // 通知PC端相机未准备好
            notifyPCError("相机未准备好，正在重新初始化...")

            // 尝试重新初始化相机
            if (allPermissionsGranted()) {
                Log.d("MainActivity", "Attempting to restart camera...")
                cameraRetryCount = 0  // 重置重试计数
                startCamera()

                // 延迟重试拍照
                Thread {
                    Thread.sleep(2000)  // 等待2秒让相机初始化
                    runOnUiThread {
                        if (this.imageCapture != null && isCameraBound) {
                            Log.d("MainActivity", "Camera ready after restart, retrying photo")
                            takePhoto()
                        } else {
                            notifyPCError("相机初始化失败，请重启应用")
                        }
                    }
                }.start()
            } else {
                notifyPCError("相机权限不足")
            }
            return
        }

        // 检查Activity状态
        if (isFinishing || isDestroyed) {
            Log.w("MainActivity", "Activity is finishing/destroyed, cannot take photo")
            notifyPCError("应用状态异常，无法拍照")
            return
        }

        Log.d("MainActivity", "Camera ready, proceeding with photo capture")

        val name = SimpleDateFormat(FILENAME_FORMAT, Locale.US)
            .format(System.currentTimeMillis())
        val contentValues = ContentValues().apply {
            put(MediaStore.MediaColumns.DISPLAY_NAME, name)
            put(MediaStore.MediaColumns.MIME_TYPE, "image/jpeg")
            put(MediaStore.Images.Media.RELATIVE_PATH, "Pictures/CameraX-Image")
        }

        val outputOptions = ImageCapture.OutputFileOptions
            .Builder(contentResolver,
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                contentValues)
            .build()

        imageCapture.takePicture(
            outputOptions,
            ContextCompat.getMainExecutor(this@MainActivity),
            object : ImageCapture.OnImageSavedCallback {
                override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                    val savedUri = output.savedUri ?: return
                    Log.d("MainActivity", "Photo capture succeeded: $savedUri")

                    // 更新状态为正在发送照片
                    runOnUiThread {
                        updateConnectionStatus("正在发送照片...")
                    }

                    // 发送照片到PC端并等待处理结果
                    notifyPhotoTaken(savedUri)

                    // 不再立即设置等待照片状态，等收到PROCESSING_COMPLETE后再设置
                    runOnUiThread {
                        updateConnectionStatus("等待PC处理照片...")
                    }
                }

                override fun onError(exception: ImageCaptureException) {
                    Log.e("MainActivity", "Photo capture failed: ${exception.message}", exception)
                    Toast.makeText(this@MainActivity, "拍照失败: ${exception.message}", Toast.LENGTH_SHORT).show()

                    // 在后台线程中通知PC端拍照失败
                    Thread {
                        clientSocket?.let { socket ->
                            try {
                                val outputStream = socket.getOutputStream()
                                val message = "ERROR:拍照失败 - ${exception.message}\n"
                                outputStream.write(message.toByteArray())
                                outputStream.flush()
                            } catch (e: IOException) {
                                Log.e("MainActivity", "Failed to notify PC about error: ${e.message}")
                            }
                        }
                    }.start()
                }
            })
    }

    private fun notifyPhotoTaken(uri: Uri) {
        // 在后台线程中发送照片到PC端
        Thread {
            clientSocket?.let { socket ->
                try {
                    sendPhotoToPC(socket, uri)
                } catch (e: Exception) {
                    Log.e("MainActivity", "Failed to send photo to PC: ${e.message}")
                }
            }
        }.start()
    }

    private fun sendPhotoToPC(socket: Socket, uri: Uri) {
        try {
            val outputStream = socket.getOutputStream()

            // 首先发送拍照完成通知
            val notifyMessage = "PHOTO_TAKEN:$uri\n"
            outputStream.write(notifyMessage.toByteArray())
            outputStream.flush()
            Log.d("MainActivity", "Notified PC: $notifyMessage")

            // 读取并处理照片数据
            val inputStream = contentResolver.openInputStream(uri)
            inputStream?.use { input ->
                val originalPhotoBytes = input.readBytes()
                Log.d("MainActivity", "Original photo size: ${originalPhotoBytes.size} bytes")

                // 根据当前方向旋转照片（物理旋转）
                val rotatedPhotoBytes = rotatePhotoToCorrectOrientation(originalPhotoBytes, currentOrientation)
                Log.d("MainActivity", "Rotated photo size: ${rotatedPhotoBytes.size} bytes, orientation: $currentOrientation°")

                // 发送照片数据头信息
                val photoHeader = "PHOTO_DATA:${rotatedPhotoBytes.size}\n"
                outputStream.write(photoHeader.toByteArray())
                outputStream.flush()
                Log.d("MainActivity", "Sending photo header: ${rotatedPhotoBytes.size} bytes")

                // 等待一小段时间确保头信息被完全发送
                Thread.sleep(50)

                // 发送照片数据（纯二进制，不添加任何文本）
                outputStream.write(rotatedPhotoBytes)
                outputStream.flush()
                Log.d("MainActivity", "Photo binary data sent: ${rotatedPhotoBytes.size} bytes")

                // 等待一小段时间确保照片数据被完全发送
                Thread.sleep(50)

                // 发送结束标记
                val endMarker = "PHOTO_END\n"
                outputStream.write(endMarker.toByteArray())
                outputStream.flush()
                Log.d("MainActivity", "Photo end marker sent")

                Log.d("MainActivity", "Photo sent successfully to PC")
            } ?: run {
                Log.e("MainActivity", "Failed to open photo input stream")
            }

        } catch (e: IOException) {
            Log.e("MainActivity", "Failed to send photo: ${e.message}")
        }
    }

    private fun rotatePhotoToCorrectOrientation(photoBytes: ByteArray, orientation: Int): ByteArray {
        return try {
            // 解码原始图片
            val originalBitmap = BitmapFactory.decodeByteArray(photoBytes, 0, photoBytes.size)
            if (originalBitmap == null) {
                Log.e("MainActivity", "Failed to decode bitmap")
                return photoBytes
            }

            Log.d("MainActivity", "Original bitmap size: ${originalBitmap.width}x${originalBitmap.height}")

            // 统一对所有方向进行90度顺时针修正（因为系统问题导致所有照片都左旋了90度）
            Log.d("MainActivity", "Applying universal 90° clockwise correction for orientation: $orientation°")

            val matrix = Matrix()
            when (orientation) {
                0 -> {
                    // 竖屏：原来0°，现在修正为+90°
                    matrix.postRotate(90f)
                    Log.d("MainActivity", "Portrait: rotating +90° (0° + 90° correction)")
                }
                90 -> {
                    // 左横屏：原来需要+90°，现在修正为+180°
                    matrix.postRotate(180f)
                    Log.d("MainActivity", "Left landscape: rotating +180° (90° + 90° correction)")
                }
                180 -> {
                    // 倒立：原来需要180°，现在修正为+270°
                    matrix.postRotate(270f)
                    Log.d("MainActivity", "Upside down: rotating +270° (180° + 90° correction)")
                }
                270 -> {
                    // 右横屏：原来需要-90°，现在修正为0°（不旋转）
                    matrix.postRotate(0f)
                    Log.d("MainActivity", "Right landscape: no rotation (-90° + 90° correction = 0°)")
                }
            }

            // 应用旋转矩阵
            val finalBitmap = if (orientation == 270) {
                // 右横屏不需要旋转，直接使用原图
                originalBitmap
            } else {
                // 其他方向都需要旋转
                val rotatedBitmap = Bitmap.createBitmap(
                    originalBitmap, 0, 0,
                    originalBitmap.width, originalBitmap.height,
                    matrix, true
                )
                Log.d("MainActivity", "Rotated bitmap size: ${rotatedBitmap.width}x${rotatedBitmap.height}")
                rotatedBitmap
            }

            // 将图片转换为标准JPEG字节数组
            val outputStream = java.io.ByteArrayOutputStream()
            finalBitmap.compress(Bitmap.CompressFormat.JPEG, 90, outputStream)
            val processedBytes = outputStream.toByteArray()

            // 清理资源
            originalBitmap.recycle()
            if (finalBitmap != originalBitmap) {
                finalBitmap.recycle()
            }
            outputStream.close()

            Log.d("MainActivity", "Photo rotation completed successfully")
            processedBytes

        } catch (e: Exception) {
            Log.e("MainActivity", "Failed to rotate photo: ${e.message}")
            // 如果旋转失败，返回原始数据
            photoBytes
        }
    }

    private fun allPermissionsGranted() = REQUIRED_PERMISSIONS.all {
        ContextCompat.checkSelfPermission(
            baseContext, it
        ) == PackageManager.PERMISSION_GRANTED
    }

    override fun onRequestPermissionsResult(
        requestCode: Int, permissions: Array<String>, grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == REQUEST_CODE_PERMISSIONS) {
            if (allPermissionsGranted()) {
                startCamera()
            } else {
                Toast.makeText(this, "权限被拒绝", Toast.LENGTH_SHORT).show()
                finish()
            }
        }
    }

    override fun onPause() {
        super.onPause()
        Log.d("MainActivity", "Activity paused")

        // 注销传感器监听器
        accelerometer?.let {
            sensorManager.unregisterListener(this, it)
            Log.d("MainActivity", "重力感应器监听已停止")
        }

        // 标记相机可能需要重新绑定
        // 注意：不直接设置为false，因为相机可能仍然可用
        Log.d("MainActivity", "Activity paused, camera binding status: $isCameraBound")
    }

    override fun onResume() {
        super.onResume()
        Log.d("MainActivity", "Activity resumed")

        // 注册传感器监听器
        accelerometer?.let {
            sensorManager.registerListener(this, it, SensorManager.SENSOR_DELAY_NORMAL)
            Log.d("MainActivity", "重力感应器监听已启动")
        }

        // 检查相机状态并重新初始化
        if (allPermissionsGranted()) {
            // 延迟一点时间确保Activity完全恢复
            Thread {
                Thread.sleep(500)
                runOnUiThread {
                    if (!isFinishing && !isDestroyed) {
                        if (!isCameraBound || imageCapture == null) {
                            Log.d("MainActivity", "Camera not properly bound, restarting...")
                            cameraRetryCount = 0
                            startCamera()
                        } else {
                            Log.d("MainActivity", "Camera already bound and ready")
                        }

                        // 根据连接状态管理广播
                        manageBroadcastBasedOnConnection()
                    }
                }
            }.start()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d("MainActivity", "Activity destroying")

        // 清除屏幕常亮标志
        window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        Log.d("MainActivity", "Screen keep on disabled - 屏幕常亮已关闭")

        // 注销传感器监听器
        accelerometer?.let {
            sensorManager.unregisterListener(this, it)
            Log.d("MainActivity", "重力感应器监听已注销")
        }

        // 停止广播服务
        stopBroadcastService()

        // 清理相机状态
        isCameraBound = false
        imageCapture = null

        cameraExecutor.shutdown()
        isServerRunning = false
        try {
            clientSocket?.close()
            if (::serverSocket.isInitialized) {
                serverSocket.close()
            }
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }

    companion object {
        private const val TAG = "CameraXApp"
        private const val FILENAME_FORMAT = "yyyy-MM-dd-HH-mm-ss-SSS"
        private const val REQUEST_CODE_PERMISSIONS = 10
        private val REQUIRED_PERMISSIONS = arrayOf(
            Manifest.permission.CAMERA,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
        )
    }

    private fun updateConnectionStatus(status: String) {
        runOnUiThread {
            findViewById<TextView>(R.id.connection_status_text).text = "📱 $status"
        }
    }





    private fun startBroadcastService() {
        // 如果已经有PC连接，不启动广播
        if (isConnectedToPC()) {
            Log.d("MainActivity", "Broadcast not started - PC already connected")
            return
        }

        try {
            broadcastService.start(deviceName, ipAddress, 8080)
            isBroadcasting = true
            Log.d("MainActivity", "Broadcast service started")
        } catch (e: Exception) {
            isBroadcasting = false
            Log.e("MainActivity", "Failed to start broadcast service: ${e.message}")
        }
    }

    private fun stopBroadcastService() {
        try {
            broadcastService.stop()
            isBroadcasting = false
            Log.d("MainActivity", "Broadcast service stopped")
        } catch (e: Exception) {
            Log.e("MainActivity", "Error stopping broadcast service: ${e.message}")
        }
    }

    private fun notifyPCError(message: String) {
        Thread {
            clientSocket?.let { socket ->
                try {
                    val outputStream = socket.getOutputStream()
                    val errorMessage = "ERROR:$message\n"
                    outputStream.write(errorMessage.toByteArray())
                    outputStream.flush()
                    Log.d("MainActivity", "Notified PC about error: $message")
                } catch (e: IOException) {
                    Log.e("MainActivity", "Failed to notify PC about error: ${e.message}")
                }
            }
        }.start()
    }

    private fun isConnectedToPC(): Boolean {
        return clientSocket != null && clientSocket?.isConnected == true && !clientSocket?.isClosed!!
    }

    private fun manageBroadcastBasedOnConnection() {
        if (isConnectedToPC()) {
            // 有PC连接时停止广播
            if (broadcastService.isRunning()) {
                stopBroadcastService()
                Log.d("MainActivity", "Broadcast stopped due to PC connection")
            }
        } else {
            // 没有PC连接时启动广播
            if (!broadcastService.isRunning()) {
                startBroadcastService()
                Log.d("MainActivity", "Broadcast restarted after PC disconnection")
            }
        }
    }

    private fun handleTaskInfo(taskJson: String) {
        try {
            // 解析任务信息JSON
            val taskInfo = parseTaskJson(taskJson)
            currentTask = taskInfo

            runOnUiThread {
                // 显示任务信息
                showTaskInfo(taskInfo)
                // 更新连接状态
                updateConnectionStatus("已接收任务: ${taskInfo["name"]}，等待拍照指令")
                Toast.makeText(this, "收到新任务: ${taskInfo["name"]}", Toast.LENGTH_SHORT).show()
            }

            Log.d("MainActivity", "收到任务信息: $taskInfo")
        } catch (e: Exception) {
            Log.e("MainActivity", "解析任务信息失败: ${e.message}")
        }
    }

    private fun parseTaskJson(json: String): Map<String, String> {
        // 简单的JSON解析（实际项目中建议使用JSON库）
        val result = mutableMapOf<String, String>()

        // 移除大括号和引号
        val cleanJson = json.replace("{", "").replace("}", "").replace("\"", "")
        val pairs = cleanJson.split(",")

        for (pair in pairs) {
            val keyValue = pair.split(":")
            if (keyValue.size == 2) {
                result[keyValue[0].trim()] = keyValue[1].trim()
            }
        }

        return result
    }

    private fun showTaskInfo(taskInfo: Map<String, String>) {
        Log.d("MainActivity", "showTaskInfo called with: $taskInfo")
        val taskInfoText = findViewById<TextView>(R.id.task_info_text)
        val taskInfoLayout = findViewById<LinearLayout>(R.id.task_info_layout)

        val infoText = buildString {
            append("姓名: ${taskInfo["name"] ?: "未知"}\n")
            append("身份证号: ${taskInfo["id_number"] ?: "未知"}")
        }

        taskInfoText.text = infoText
        taskInfoLayout.visibility = View.VISIBLE
        Log.d("MainActivity", "Task info displayed: $infoText")
    }

    private fun handleProcessedPhotoHeader(photoSize: Int) {
        Log.d("MainActivity", "准备接收处理后照片: $photoSize 字节")
        waitingForProcessedPhoto = true
        expectedProcessedPhotoSize = photoSize
        processedPhotoBuffer.clear()

        runOnUiThread {
            Toast.makeText(this, "正在接收处理后照片...", Toast.LENGTH_SHORT).show()
        }
    }

    private fun handleProcessedPhotoData(buffer: ByteArray, bytesRead: Int) {
        // 将接收到的数据添加到照片缓冲区
        for (i in 0 until bytesRead) {
            processedPhotoBuffer.add(buffer[i])
        }

        val progress = (processedPhotoBuffer.size.toFloat() / expectedProcessedPhotoSize * 100).toInt()
        Log.d("MainActivity", "接收处理后照片数据: $bytesRead 字节, 进度: $progress%")

        // 检查是否接收完成
        if (processedPhotoBuffer.size >= expectedProcessedPhotoSize) {
            handleProcessedPhotoComplete()
        }
    }

    private fun handleProcessedPhotoComplete() {
        waitingForProcessedPhoto = false

        if (processedPhotoBuffer.isNotEmpty()) {
            val photoData = processedPhotoBuffer.toByteArray()
            Log.d("MainActivity", "处理后照片接收完成: ${photoData.size} 字节")

            runOnUiThread {
                showProcessedPhotoConfirmation(photoData)
            }
        }
    }

    private fun showProcessedPhotoConfirmation(photoData: ByteArray) {
        // 在相机预览框中显示处理后的照片
        val processedPhotoPreview = findViewById<ImageView>(R.id.processed_photo_preview)
        val photoActionButtons = findViewById<LinearLayout>(R.id.photo_action_buttons)
        val viewFinder = findViewById<PreviewView>(R.id.viewFinder)
        val captureButton = findViewById<Button>(R.id.capture_button)
        val confirmButton = findViewById<Button>(R.id.confirm_photo_button)
        val retakeButton = findViewById<Button>(R.id.retake_photo_button)

        // 显示处理后的照片
        Log.d("MainActivity", "尝试解码照片数据，大小: ${photoData.size} 字节")
        val bitmap = BitmapFactory.decodeByteArray(photoData, 0, photoData.size)
        if (bitmap != null) {
            Log.d("MainActivity", "照片解码成功，尺寸: ${bitmap.width}x${bitmap.height}")
            processedPhotoPreview.setImageBitmap(bitmap)

            // 隐藏相机预览和拍照按钮，显示照片预览和操作按钮
            viewFinder.visibility = View.GONE
            captureButton.visibility = View.GONE
            processedPhotoPreview.visibility = View.VISIBLE
            photoActionButtons.visibility = View.VISIBLE

            Log.d("MainActivity", "处理后照片已显示在预览框中")
        } else {
            Log.e("MainActivity", "无法解码处理后的照片数据，数据大小: ${photoData.size} 字节")
            // 打印前100个字节用于调试
            val debugData = photoData.take(100).joinToString(" ") { "%02x".format(it) }
            Log.e("MainActivity", "照片数据前100字节: $debugData")
            Toast.makeText(this, "照片显示失败", Toast.LENGTH_SHORT).show()
            return
        }

        confirmButton.setOnClickListener {
            // 确认照片
            handlePhotoConfirmation(true)
        }

        retakeButton.setOnClickListener {
            // 重新拍照
            handlePhotoConfirmation(false)
        }
    }

    private fun restoreCameraView() {
        // 恢复相机预览界面
        val processedPhotoPreview = findViewById<ImageView>(R.id.processed_photo_preview)
        val photoActionButtons = findViewById<LinearLayout>(R.id.photo_action_buttons)
        val viewFinder = findViewById<PreviewView>(R.id.viewFinder)
        val captureButton = findViewById<Button>(R.id.capture_button)

        processedPhotoPreview.visibility = View.GONE
        photoActionButtons.visibility = View.GONE
        viewFinder.visibility = View.VISIBLE
        captureButton.visibility = View.VISIBLE
    }

    private fun handlePhotoConfirmation(confirmed: Boolean) {
        // 发送确认结果到PC端
        sendConfirmation(confirmed)

        // 执行状态同步逻辑
        syncPhotoConfirmationState(confirmed)
    }

    private fun syncPhotoConfirmationState(confirmed: Boolean) {
        // 仅同步状态，不发送消息到PC端
        if (confirmed) {
            // 确认照片
            clearCurrentTask()
            // 恢复相机预览
            restoreCameraView()
            // 更新状态为等待新任务
            updateConnectionStatus("等待新任务...")
        } else {
            // 重新拍照
            // 恢复相机预览
            restoreCameraView()
            // 不清除任务，直接进入等待PC发送照片状态
            updateConnectionStatus("等待重新拍照...")
        }
    }

    private fun sendConfirmation(confirmed: Boolean) {
        Thread {
            clientSocket?.let { socket ->
                try {
                    val outputStream = socket.getOutputStream()
                    val message = if (confirmed) "PHOTO_CONFIRMED\n" else "PHOTO_REJECTED\n"
                    outputStream.write(message.toByteArray())
                    outputStream.flush()

                    Log.d("MainActivity", "发送确认结果: $message")
                } catch (e: Exception) {
                    Log.e("MainActivity", "发送确认失败: ${e.message}")
                }
            }
        }.start()
    }

    private fun clearCurrentTask() {
        currentTask = null
        processedPhotoData = null
        processedPhotoBuffer.clear()
        waitingForProcessedPhoto = false
        expectedProcessedPhotoSize = 0

        runOnUiThread {
            val taskInfoLayout = findViewById<LinearLayout>(R.id.task_info_layout)
            taskInfoLayout.visibility = View.GONE
            Toast.makeText(this, "任务已完成", Toast.LENGTH_SHORT).show()
        }
    }

    private fun showMenuDialog() {
        val dialogView = layoutInflater.inflate(R.layout.menu_dialog, null)

        // 获取菜单对话框中的控件
        val deviceNameEdit = dialogView.findViewById<EditText>(R.id.device_name_edit)
        val saveDeviceNameButton = dialogView.findViewById<Button>(R.id.save_device_name_button)
        val ipAddressText = dialogView.findViewById<TextView>(R.id.ip_address_text)
        val screenStatusText = dialogView.findViewById<TextView>(R.id.screen_status_text)
        val broadcastStatusText = dialogView.findViewById<TextView>(R.id.broadcast_status_text)
        val orientationStatusText = dialogView.findViewById<TextView>(R.id.orientation_status_text)

        // 设置当前设备名称
        deviceNameEdit.setText(deviceName)

        // 更新状态信息
        ipAddressText.text = "IP地址: $ipAddress"
        screenStatusText.text = "🔆 屏幕保持常亮"
        broadcastStatusText.text = if (isBroadcasting) "📡 广播: 已启动" else "📡 广播: 已停止"
        orientationStatusText.text = "🧭 方向: ${if (resources.configuration.orientation == android.content.res.Configuration.ORIENTATION_PORTRAIT) "竖屏" else "横屏"}"

        val dialog = AlertDialog.Builder(this)
            .setTitle("菜单")
            .setView(dialogView)
            .setNegativeButton("关闭", null)
            .create()

        // 设置保存设备名称按钮事件
        saveDeviceNameButton.setOnClickListener {
            val newDeviceName = deviceNameEdit.text.toString().trim()
            if (newDeviceName.isNotEmpty()) {
                saveDeviceName(newDeviceName)
                Toast.makeText(this, "设备名称已保存", Toast.LENGTH_SHORT).show()
            } else {
                Toast.makeText(this, "请输入设备名称", Toast.LENGTH_SHORT).show()
            }
        }

        dialog.show()
    }

    private fun saveDeviceName(newDeviceName: String) {
        deviceName = newDeviceName

        // 保存到SharedPreferences
        sharedPreferences.edit()
            .putString("device_name", deviceName)
            .apply()

        // 更新广播服务
        broadcastService.updateDeviceInfo(deviceName, ipAddress, 8080)

        Log.d("MainActivity", "Device name updated: $deviceName")
    }

    private fun findBytePattern(buffer: ByteArray, pattern: ByteArray, bufferLength: Int): Int {
        for (i in 0..bufferLength - pattern.size) {
            var found = true
            for (j in pattern.indices) {
                if (buffer[i + j] != pattern[j]) {
                    found = false
                    break
                }
            }
            if (found) {
                return i
            }
        }
        return -1
    }

    private fun initSensor() {
        sensorManager = getSystemService(Context.SENSOR_SERVICE) as SensorManager
        accelerometer = sensorManager.getDefaultSensor(Sensor.TYPE_ACCELEROMETER)

        if (accelerometer != null) {
            Log.d("MainActivity", "重力感应器初始化成功")
        } else {
            Log.w("MainActivity", "设备不支持重力感应器")
        }
    }

    override fun onSensorChanged(event: SensorEvent?) {
        if (event?.sensor?.type == Sensor.TYPE_ACCELEROMETER) {
            val x = event.values[0]
            val y = event.values[1]
            val z = event.values[2]

            // 计算设备方向
            val newOrientation = calculateOrientation(x, y, z)

            if (newOrientation != currentOrientation) {
                currentOrientation = newOrientation
                val orientationText = when (currentOrientation) {
                    0 -> "竖屏"
                    90 -> "左横屏"
                    180 -> "倒立"
                    270 -> "右横屏"
                    else -> "未知"
                }
                Log.d("MainActivity", "设备方向变化: $orientationText ($currentOrientation°)")

                // 更新相机方向
                updateCameraOrientation()
            }
        }
    }

    override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {
        // 传感器精度变化时的处理（通常不需要特殊处理）
    }

    private fun calculateOrientation(x: Float, y: Float, z: Float): Int {
        // 使用重力加速度值计算设备方向
        val threshold = 5.0f // 阈值，避免频繁切换

        return when {
            y > threshold -> 0    // 竖屏（正常）
            x > threshold -> 270  // 右横屏
            y < -threshold -> 180 // 倒立
            x < -threshold -> 90  // 左横屏
            else -> currentOrientation // 保持当前方向
        }
    }

    private fun updateCameraOrientation() {
        // 更新ImageCapture的方向
        imageCapture?.targetRotation = when (currentOrientation) {
            0 -> android.view.Surface.ROTATION_0    // 竖屏
            90 -> android.view.Surface.ROTATION_90  // 左横屏
            180 -> android.view.Surface.ROTATION_180 // 倒立
            270 -> android.view.Surface.ROTATION_270 // 右横屏
            else -> android.view.Surface.ROTATION_0
        }

        Log.d("MainActivity", "相机方向已更新: $currentOrientation°")
    }

    private fun getIPAddress(): String {
        val wifiManager = getSystemService(Context.WIFI_SERVICE) as WifiManager
        val wifiInfo = wifiManager.connectionInfo
        val ipAddress = wifiInfo.ipAddress
        return String.format("%d.%d.%d.%d",
            (ipAddress and 0xff),
            (ipAddress shr 8 and 0xff),
            (ipAddress shr 16 and 0xff),
            (ipAddress shr 24 and 0xff)
        )
    }

    private fun startServer() {
        Thread {
            try {
                serverSocket = ServerSocket(8080)
                isServerRunning = true
                Log.d("MainActivity", "Server started on port 8080")

                while (isServerRunning) {
                    try {
                        val socket = serverSocket.accept()
                        val clientAddress = socket.remoteSocketAddress.toString()
                        Log.d("MainActivity", "Client connected: $clientAddress")

                        // 关闭之前的连接
                        clientSocket?.close()
                        clientSocket = socket
                        connectedPcInfo = clientAddress

                        // 停止广播服务，防止其他设备连接
                        stopBroadcastService()

                        // 更新连接状态
                        updateConnectionStatus("PC已连接: $clientAddress")

                        // 在新线程中处理客户端
                        Thread { handleClient(socket) }.start()

                    } catch (e: IOException) {
                        if (isServerRunning) {
                            Log.e("MainActivity", "Error accepting client: ${e.message}")
                        }
                    }
                }
            } catch (e: IOException) {
                Log.e("MainActivity", "Server error: ${e.message}")
            }
        }.start()
    }

    private fun handleClient(socket: Socket) {
        try {
            val inputStream = socket.getInputStream()
            val outputStream = socket.getOutputStream()
            val buffer = ByteArray(1024)

            // 发送连接确认
            outputStream.write("CONNECTED\n".toByteArray())
            outputStream.flush()

            Log.d("MainActivity", "Client handler started")

            while (!socket.isClosed && socket.isConnected) {
                try {
                    val bytesRead = inputStream.read(buffer)
                    if (bytesRead == -1) {
                        break // 客户端断开连接
                    }

                    // 如果正在等待处理后照片数据，直接处理二进制数据
                    if (waitingForProcessedPhoto) {
                        // 检查是否包含PHOTO_END标记
                        val dataStr = String(buffer, 0, bytesRead)
                        if (dataStr.contains("PHOTO_END")) {
                            // 找到结束标记，分离照片数据和结束标记
                            val endMarkerBytes = "PHOTO_END".toByteArray()
                            val endMarkerPos = findBytePattern(buffer, endMarkerBytes, bytesRead)
                            if (endMarkerPos >= 0) {
                                // 添加结束标记前的数据
                                for (i in 0 until endMarkerPos) {
                                    processedPhotoBuffer.add(buffer[i])
                                }
                                handleProcessedPhotoComplete()
                            }
                        } else {
                            // 普通照片数据
                            handleProcessedPhotoData(buffer, bytesRead)
                        }
                        continue
                    }

                    // 处理文本命令
                    val message = String(buffer, 0, bytesRead).trim()
                    Log.d("MainActivity", "Received command: $message")

                    when {
                        message == "TAKE_PHOTO" -> {
                            // 立即确认收到命令
                            outputStream.write("COMMAND_RECEIVED\n".toByteArray())
                            outputStream.flush()

                            // 在主线程中显示提示和执行拍照
                            runOnUiThread {
                                Toast.makeText(this@MainActivity, "收到拍照指令", Toast.LENGTH_SHORT).show()
                                takePhoto()
                            }
                        }
                        message.startsWith("TASK_INFO:") -> {
                            // 接收任务信息
                            val taskJson = message.substring(10) // 去掉"TASK_INFO:"前缀
                            handleTaskInfo(taskJson)

                            // 确认收到任务信息
                            outputStream.write("TASK_RECEIVED\n".toByteArray())
                            outputStream.flush()
                        }
                        message == "PROCESSING_COMPLETE" -> {
                            // PC端处理完成，准备接收照片
                            runOnUiThread {
                                Log.d("MainActivity", "PC端处理完成，准备接收照片")
                                Toast.makeText(this@MainActivity, "照片处理完成，准备接收...", Toast.LENGTH_SHORT).show()
                            }
                            // 发送确认消息，表示准备接收照片
                            outputStream.write("READY_TO_RECEIVE\n".toByteArray())
                            outputStream.flush()
                        }
                        message.startsWith("PROCESSED_PHOTO:") -> {
                            // 接收处理后照片大小信息
                            val photoSize = message.substring(16).toIntOrNull() ?: 0
                            if (photoSize > 0) {
                                handleProcessedPhotoHeader(photoSize)
                                outputStream.write("READY_FOR_PHOTO\n".toByteArray())
                                outputStream.flush()
                            }
                        }
                        message == "PING" -> {
                            outputStream.write("PONG\n".toByteArray())
                            outputStream.flush()
                        }
                        message.startsWith("ERROR:") -> {
                            // 接收PC端错误消息
                            val errorMessage = message.substring(6) // 去掉"ERROR:"前缀
                            runOnUiThread {
                                Log.e("MainActivity", "PC端处理错误: $errorMessage")
                                Toast.makeText(this@MainActivity, "处理失败: $errorMessage", Toast.LENGTH_LONG).show()
                                updateConnectionStatus("处理失败: $errorMessage")
                                // 恢复相机预览，允许重新拍照
                                restoreCameraView()
                            }
                        }
                        message == "PC_CONFIRMED" -> {
                            // PC端确认了照片
                            runOnUiThread {
                                Log.d("MainActivity", "PC端已确认照片")
                                Toast.makeText(this@MainActivity, "PC端已确认照片", Toast.LENGTH_SHORT).show()
                                // 仅同步状态，不发送确认消息回PC端
                                syncPhotoConfirmationState(true)
                            }
                        }
                        message == "PC_REJECTED" -> {
                            // PC端取消了照片
                            runOnUiThread {
                                Log.d("MainActivity", "PC端已取消照片")
                                Toast.makeText(this@MainActivity, "PC端已取消，准备重拍", Toast.LENGTH_SHORT).show()
                                // 仅同步状态，不发送确认消息回PC端
                                syncPhotoConfirmationState(false)
                            }
                        }
                        message == "DISCONNECT" -> {
                            Log.d("MainActivity", "Client requested disconnect")
                            break
                        }
                        else -> {
                            Log.w("MainActivity", "Unknown command: $message")
                        }
                    }
                } catch (e: IOException) {
                    Log.e("MainActivity", "Error reading from client: ${e.message}")
                    break
                }
            }
        } catch (e: IOException) {
            Log.e("MainActivity", "Client handler error: ${e.message}")
        } finally {
            try {
                if (socket == clientSocket) {
                    clientSocket = null
                    connectedPcInfo = null

                    // 清除当前任务
                    runOnUiThread {
                        clearCurrentTask()
                        Log.d("MainActivity", "断开连接时已清除当前任务")
                    }

                    // 重新启动广播服务，允许其他设备连接
                    startBroadcastService()

                    // 更新连接状态
                    updateConnectionStatus("等待PC连接...")
                }
                socket.close()
                Log.d("MainActivity", "Client disconnected")
            } catch (e: IOException) {
                Log.e("MainActivity", "Error closing client socket: ${e.message}")
            }
        }
    }


}