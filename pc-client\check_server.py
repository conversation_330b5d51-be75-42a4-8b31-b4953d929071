#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查服务端状态
"""

import socket
import sys
import time

def check_port(host, port, timeout=5):
    """检查指定端口是否开放"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except Exception as e:
        print(f"检查端口时出错: {e}")
        return False

def scan_common_ports(host):
    """扫描常用端口"""
    common_ports = [8080, 8888, 8889, 9090, 9091, 3000, 5000, 8000]
    
    print(f"扫描 {host} 的常用端口...")
    open_ports = []
    
    for port in common_ports:
        print(f"检查端口 {port}...", end=" ")
        if check_port(host, port, timeout=2):
            print("✅ 开放")
            open_ports.append(port)
        else:
            print("❌ 关闭")
    
    return open_ports

def check_server_status(host="***********"):
    """检查服务端状态"""
    print("=" * 50)
    print("服务端状态检查工具")
    print("=" * 50)
    
    # 检查主机是否可达
    print(f"1. 检查主机 {host} 是否可达...")
    try:
        # 尝试ping主机（通过连接一个通常开放的端口）
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        # 尝试连接80端口（HTTP）
        result = sock.connect_ex((host, 80))
        sock.close()
        
        if result == 0:
            print(f"✅ 主机 {host} 可达（80端口开放）")
        else:
            # 再尝试443端口（HTTPS）
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((host, 443))
            sock.close()
            
            if result == 0:
                print(f"✅ 主机 {host} 可达（443端口开放）")
            else:
                print(f"⚠️ 主机 {host} 可能不可达或防火墙阻止连接")
    except Exception as e:
        print(f"❌ 检查主机时出错: {e}")
    
    print()
    
    # 检查配置的服务端端口
    print("2. 检查配置的服务端端口...")
    server_port = 9090
    if check_port(host, server_port):
        print(f"✅ 服务端端口 {server_port} 开放")
        return True
    else:
        print(f"❌ 服务端端口 {server_port} 关闭")
    
    print()
    
    # 扫描其他端口
    print("3. 扫描其他可能的端口...")
    open_ports = scan_common_ports(host)
    
    if open_ports:
        print(f"\n发现开放的端口: {open_ports}")
        print("提示: 如果服务端在其他端口运行，请检查配置文件")
    else:
        print("\n未发现开放的端口")
        print("提示: 请确保服务端已启动")
    
    return False

def get_local_ip():
    """获取本机IP地址"""
    try:
        # 连接到一个远程地址来获取本机IP
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.connect(("*******", 80))
        local_ip = sock.getsockname()[0]
        sock.close()
        return local_ip
    except:
        return "unknown"

def main():
    """主函数"""
    # 获取本机IP
    local_ip = get_local_ip()
    print(f"本机IP地址: {local_ip}")
    print()
    
    # 检查服务端状态
    server_running = check_server_status()
    
    print("\n" + "=" * 50)
    if server_running:
        print("✅ 服务端正在运行，可以进行连接测试")
        print("建议运行: python test_connection.py")
    else:
        print("❌ 服务端未运行或端口不正确")
        print("建议:")
        print("1. 检查服务端是否已启动")
        print("2. 检查防火墙设置")
        print("3. 检查端口配置是否正确")
        print("4. 如果在同一台机器上，尝试使用 127.0.0.1 或 localhost")
    
    return server_running

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
