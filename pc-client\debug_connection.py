#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试PC端连接问题
"""

import sys
import os
import traceback

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试导入"""
    print("=== 测试导入 ===")
    
    try:
        import time
        print(f"✅ time模块导入成功: {time}")
        print(f"   time.time(): {time.time()}")
    except Exception as e:
        print(f"❌ time模块导入失败: {e}")
        return False
    
    try:
        import socket
        print(f"✅ socket模块导入成功: {socket}")
    except Exception as e:
        print(f"❌ socket模块导入失败: {e}")
        return False
    
    try:
        from core.machine_id import get_simple_machine_id, get_machine_info
        print("✅ machine_id模块导入成功")
        
        machine_id = get_simple_machine_id()
        print(f"   机器码: {machine_id}")
        
        machine_info = get_machine_info()
        print(f"   机器信息: {machine_info}")
    except Exception as e:
        print(f"❌ machine_id模块导入失败: {e}")
        traceback.print_exc()
        return False
    
    try:
        import config
        print(f"✅ config模块导入成功")
        print(f"   SERVER_PORT: {config.SERVER_PORT}")
    except Exception as e:
        print(f"❌ config模块导入失败: {e}")
        return False
    
    return True

def test_server_client_import():
    """测试ServerClient导入"""
    print("\n=== 测试ServerClient导入 ===")
    
    try:
        from core.server_client import ServerClient
        print("✅ ServerClient导入成功")
        
        # 创建实例
        client = ServerClient()
        print("✅ ServerClient实例创建成功")
        
        return client
    except Exception as e:
        print(f"❌ ServerClient导入失败: {e}")
        traceback.print_exc()
        return None

def test_connection_step_by_step(client, host="***********"):
    """逐步测试连接"""
    print(f"\n=== 逐步测试连接到 {host} ===")
    
    if not client:
        print("❌ 客户端实例为空")
        return False
    
    try:
        import socket
        import time
        import config
        
        print("1. 创建socket...")
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(config.SERVER_CONNECTION_TIMEOUT)
        print("✅ socket创建成功")
        
        print(f"2. 连接到 {host}:{config.SERVER_PORT}...")
        sock.connect((host, config.SERVER_PORT))
        print("✅ 连接成功")
        
        print("3. 获取机器码...")
        from core.machine_id import get_simple_machine_id, get_machine_info
        machine_id = get_simple_machine_id()
        machine_info = get_machine_info()
        print(f"✅ 机器码获取成功: {machine_id}")
        
        print("4. 准备连接消息...")
        connect_msg = {
            "type": "connect",
            "client_info": {
                "client_type": "pc_client",
                "version": config.APP_VERSION,
                "machine_id": machine_id,
                "machine_info": machine_info
            }
        }
        print("✅ 连接消息准备完成")
        
        print("5. 发送连接消息...")
        import json
        message_str = json.dumps(connect_msg)
        sock.send(message_str.encode('utf-8'))
        print(f"✅ 消息发送成功，长度: {len(message_str)}")
        
        print("6. 等待响应...")
        sock.settimeout(15.0)
        response_data = sock.recv(8192).decode('utf-8')
        print(f"✅ 收到响应，长度: {len(response_data)}")
        
        print("7. 解析响应...")
        response = json.loads(response_data)
        print(f"✅ 响应解析成功: {response}")
        
        sock.close()
        print("✅ 连接测试完成")
        
        return response.get('status') == 'success'
        
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        traceback.print_exc()
        try:
            sock.close()
        except:
            pass
        return False

def test_server_client_connect(client, host="***********"):
    """测试ServerClient连接方法"""
    print(f"\n=== 测试ServerClient连接方法 ===")
    
    if not client:
        print("❌ 客户端实例为空")
        return False
    
    try:
        print(f"调用 connect_to_server({host})...")
        result = client.connect_to_server(host)
        print(f"连接结果: {result}")
        
        if result:
            print("✅ ServerClient连接成功")
            
            # 断开连接
            client.disconnect_from_server()
            print("✅ 连接已断开")
        else:
            print("❌ ServerClient连接失败")
        
        return result
        
    except Exception as e:
        print(f"❌ ServerClient连接异常: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("PC端连接调试工具")
    print("=" * 50)
    
    # 测试导入
    if not test_imports():
        print("❌ 导入测试失败，无法继续")
        return False
    
    # 测试ServerClient导入
    client = test_server_client_import()
    if not client:
        print("❌ ServerClient导入失败，无法继续")
        return False
    
    # 逐步测试连接
    step_result = test_connection_step_by_step(client)
    
    # 测试ServerClient连接方法
    client_result = test_server_client_connect(client)
    
    print("\n" + "=" * 50)
    print("调试结果:")
    print(f"逐步连接测试: {'成功' if step_result else '失败'}")
    print(f"ServerClient连接: {'成功' if client_result else '失败'}")
    
    if step_result and client_result:
        print("✅ 所有测试通过！")
        return True
    elif step_result and not client_result:
        print("⚠️ 基础连接正常，但ServerClient有问题")
        return False
    else:
        print("❌ 连接测试失败！")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
