@echo off
echo Building 32-bit ID Card Bridge...

REM Set up MSVC environment for 32-bit
call "C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Auxiliary\Build\vcvars32.bat"

REM Compile the bridge program
echo Compiling...
cl /EHsc /Fe:id_card_bridge.exe id_card_bridge.cpp ws2_32.lib

if %ERRORLEVEL% EQU 0 (
    echo Build successful! Generated id_card_bridge.exe
    dir id_card_bridge.exe
) else (
    echo Build failed!
)

pause
