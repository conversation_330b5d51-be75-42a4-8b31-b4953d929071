# PowerShell脚本：编译32位身份证桥接程序

Write-Host "编译32位身份证桥接程序..." -ForegroundColor Green

# Visual Studio Build Tools 2022路径
$vsPath = "C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools"
$vcvarsPath = "$vsPath\VC\Auxiliary\Build\vcvars32.bat"

# 检查vcvars32.bat是否存在
if (-not (Test-Path $vcvarsPath)) {
    Write-Host "错误: 未找到 vcvars32.bat" -ForegroundColor Red
    Write-Host "路径: $vcvarsPath" -ForegroundColor Yellow
    
    # 尝试查找其他可能的路径
    $possiblePaths = @(
        "C:\Program Files (x86)\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars32.bat",
        "C:\Program Files (x86)\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars32.bat",
        "C:\Program Files (x86)\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars32.bat",
        "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars32.bat",
        "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars32.bat",
        "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars32.bat"
    )
    
    foreach ($path in $possiblePaths) {
        if (Test-Path $path) {
            Write-Host "找到替代路径: $path" -ForegroundColor Green
            $vcvarsPath = $path
            break
        }
    }
    
    if (-not (Test-Path $vcvarsPath)) {
        Write-Host "错误: 无法找到Visual Studio环境设置脚本" -ForegroundColor Red
        Write-Host "请确保已安装Visual Studio或Build Tools" -ForegroundColor Yellow
        Read-Host "按任意键退出"
        exit 1
    }
}

Write-Host "使用环境设置脚本: $vcvarsPath" -ForegroundColor Yellow

# 创建临时批处理文件来设置环境并编译
$tempBat = "temp_build.bat"
$batContent = @'
@echo off
call "{0}"
echo 正在编译32位程序...
cl /EHsc /Fe:id_card_bridge.exe id_card_bridge.cpp ws2_32.lib
if errorlevel 1 (
    echo 编译失败
    exit /b 1
) else (
    echo 编译成功！生成了 id_card_bridge.exe
    echo 注意：这是32位程序，可以加载32位DLL
)
'@ -f $vcvarsPath

# 写入临时批处理文件
$batContent | Out-File -FilePath $tempBat -Encoding ASCII

try {
    # 执行编译
    Write-Host "开始编译..." -ForegroundColor Yellow
    $process = Start-Process -FilePath $tempBat -Wait -PassThru -NoNewWindow
    
    if ($process.ExitCode -eq 0) {
        Write-Host "编译成功！" -ForegroundColor Green
        
        # 检查生成的文件
        if (Test-Path "id_card_bridge.exe") {
            $fileInfo = Get-Item "id_card_bridge.exe"
            Write-Host "生成文件: $($fileInfo.Name)" -ForegroundColor Green
            Write-Host "文件大小: $($fileInfo.Length) 字节" -ForegroundColor Green
            Write-Host "修改时间: $($fileInfo.LastWriteTime)" -ForegroundColor Green
        }
    } else {
        Write-Host "编译失败，退出代码: $($process.ExitCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "执行编译时出错: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    # 清理临时文件
    if (Test-Path $tempBat) {
        Remove-Item $tempBat -Force
    }
}

Write-Host "按任意键退出..." -ForegroundColor Yellow
Read-Host
