#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HivisionIDPhotos API客户端
用于与HivisionIDPhotos API服务进行通信
"""

import requests
import base64
import io
import json
import logging
import os
import tempfile
from typing import Dict, Any, Optional, Union
from PIL import Image
import config
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

logger = logging.getLogger(__name__)

class HivisionAPIClient:
    """HivisionIDPhotos API客户端类"""

    def __init__(self, api_url: str = None, timeout: int = None):
        """
        初始化API客户端

        Args:
            api_url: API服务地址，默认使用配置文件中的地址
            timeout: 请求超时时间，默认使用配置文件中的时间
        """
        self.api_url = api_url or config.HIVISION_API_URL
        self.timeout = timeout or config.HIVISION_TIMEOUT
        self.session = requests.Session()
        self.use_fallback_port = False  # 是否使用备用端口

        # 设置请求头
        self.session.headers.update({
            'User-Agent': f'StudentPhoto/{config.APP_VERSION}'
        })

    def _get_api_url(self) -> str:
        """
        获取API URL，如果需要使用备用端口则添加端口号

        Returns:
            str: 完整的API URL
        """
        if self.use_fallback_port:
            # 解析原URL，替换为端口8889（代理环境）
            if self.api_url.startswith('https://'):
                # 移除https://前缀，添加端口8889，保持https协议
                domain = self.api_url[8:]  # 移除https://
                if ':' in domain:
                    domain = domain.split(':')[0]  # 移除现有端口
                return f"https://{domain}:8889"
            else:
                return self.api_url
        return self.api_url

    def _make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """
        发起HTTP请求，自动处理SSL证书错误

        Args:
            method: HTTP方法
            endpoint: API端点
            **kwargs: 其他请求参数

        Returns:
            requests.Response: 响应对象
        """
        url = f"{self._get_api_url()}{endpoint}"

        try:
            # 首次尝试正常请求
            response = self.session.request(method, url, timeout=self.timeout, **kwargs)
            return response
        except requests.exceptions.SSLError as e:
            if "CERTIFICATE_VERIFY_FAILED" in str(e) and not self.use_fallback_port:
                logger.warning("SSL certificate verification failed, trying port 8889")
                self.use_fallback_port = True
                # 重新构建URL并重试
                url = f"{self._get_api_url()}{endpoint}"
                response = self.session.request(method, url, timeout=self.timeout, verify=False, **kwargs)
                return response
            else:
                raise
        
    def check_service(self) -> bool:
        """
        检查API服务是否可用

        Returns:
            bool: 服务是否可用
        """
        # 尝试多个可能的端点
        endpoints_to_try = ["/docs", "/", "/api/docs", "/swagger"]

        for endpoint in endpoints_to_try:
            try:
                response = self._make_request("GET", endpoint)
                if response.status_code == 200:
                    return True
            except Exception:
                continue

        # 如果所有端点都失败，尝试POST到idphoto端点看是否有响应
        try:
            response = self._make_request("POST", "/idphoto", data={"test": "connection"})
            # 如果返回422（参数错误）或其他非500错误，说明服务是可用的
            if response.status_code in [422, 400, 405]:
                return True
        except Exception as e:
            logger.error(f"Service check failed: {e}")

        return False
    
    def _image_to_base64(self, image_input: Union[str, Image.Image, bytes]) -> str:
        """
        将图像转换为base64编码

        Args:
            image_input: 图像输入，可以是文件路径、PIL图像对象或字节数据

        Returns:
            str: base64编码的图像数据
        """
        if isinstance(image_input, str):
            # 文件路径
            with open(image_input, 'rb') as f:
                image_bytes = f.read()
        elif isinstance(image_input, Image.Image):
            # PIL图像对象
            buffer = io.BytesIO()
            image_input.save(buffer, format='JPEG')
            image_bytes = buffer.getvalue()
        elif isinstance(image_input, bytes):
            # 字节数据
            image_bytes = image_input
        else:
            raise ValueError("不支持的图像输入类型")

        # 确保base64编码正确
        base64_str = base64.b64encode(image_bytes).decode('utf-8')

        # 验证base64编码的正确性
        try:
            # 尝试解码验证
            base64.b64decode(base64_str)
            logger.debug(f"Base64编码验证成功，长度: {len(base64_str)}")
        except Exception as e:
            logger.error(f"Base64编码验证失败: {e}")
            raise ValueError(f"Base64编码生成失败: {e}")

        return base64_str
    
    def _base64_to_image(self, base64_str: str) -> Image.Image:
        """
        将base64编码转换为PIL图像对象

        Args:
            base64_str: base64编码的图像数据

        Returns:
            Image.Image: PIL图像对象
        """
        # 修复base64填充问题
        base64_str = base64_str.strip()
        missing_padding = len(base64_str) % 4
        if missing_padding:
            base64_str += '=' * (4 - missing_padding)

        image_bytes = base64.b64decode(base64_str)
        return Image.open(io.BytesIO(image_bytes))
    
    def create_id_photo(self, 
                       image_input: Union[str, Image.Image, bytes],
                       height: int = 413,
                       width: int = 295,
                       human_matting_model: str = "modnet_photographic_portrait_matting",
                       face_detect_model: str = "mtcnn",
                       hd: bool = True,
                       dpi: int = 300,
                       face_alignment: bool = True,
                       head_measure_ratio: float = 0.2,
                       head_height_ratio: float = 0.45,
                       top_distance_max: float = 0.12,
                       top_distance_min: float = 0.1,
                       brightness_strength: float = 0,
                       contrast_strength: float = 0,
                       sharpen_strength: float = 0,
                       saturation_strength: float = 0) -> Dict[str, Any]:
        """
        生成证件照（底透明）
        
        Args:
            image_input: 输入图像
            height: 标准证件照高度
            width: 标准证件照宽度
            human_matting_model: 人像分割模型
            face_detect_model: 人脸检测模型
            hd: 是否生成高清证件照
            dpi: 图像分辨率
            face_alignment: 是否进行人脸对齐
            head_measure_ratio: 面部面积与照片面积的比例
            head_height_ratio: 面部中心与照片顶部的高度比例
            top_distance_max: 头部与照片顶部距离的比例最大值
            top_distance_min: 头部与照片顶部距离的比例最小值
            brightness_strength: 亮度调整强度
            contrast_strength: 对比度调整强度
            sharpen_strength: 锐化调整强度
            saturation_strength: 饱和度调整强度
            
        Returns:
            Dict[str, Any]: API响应结果
        """
        try:
            # 准备请求数据
            data = {
                "height": height,
                "width": width,
                "human_matting_model": human_matting_model,
                "face_detect_model": face_detect_model,
                "hd": hd,
                "dpi": dpi,
                "face_alignment": face_alignment,
                "head_measure_ratio": head_measure_ratio,
                "head_height_ratio": head_height_ratio,
                "top_distance_max": top_distance_max,
                "top_distance_min": top_distance_min,
                "brightness_strength": brightness_strength,
                "contrast_strength": contrast_strength,
                "sharpen_strength": sharpen_strength,
                "saturation_strength": saturation_strength,
            }
            
            # 优先使用文件上传，避免base64编码问题
            if isinstance(image_input, str):
                # 直接使用文件路径上传
                with open(image_input, 'rb') as f:
                    files = {"input_image": f}
                    response = self._make_request("POST", "/idphoto", files=files, data=data)
            else:
                # 对于非文件路径输入，先保存为临时文件再上传
                import tempfile
                temp_file = None
                try:
                    # 创建临时文件
                    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.jpg')

                    if isinstance(image_input, Image.Image):
                        # PIL图像对象
                        image_input.save(temp_file.name, format='JPEG')
                    elif isinstance(image_input, bytes):
                        # 字节数据
                        temp_file.write(image_input)
                        temp_file.close()
                    else:
                        raise ValueError("不支持的图像输入类型")

                    # 使用临时文件上传
                    with open(temp_file.name, 'rb') as f:
                        files = {"input_image": f}
                        response = self._make_request("POST", "/idphoto", files=files, data=data)

                finally:
                    # 清理临时文件
                    if temp_file and os.path.exists(temp_file.name):
                        os.unlink(temp_file.name)
            
            response.raise_for_status()

            # 检查响应内容类型
            content_type = response.headers.get('content-type', '')
            if 'application/json' not in content_type:
                logger.error(f"Unexpected content type: {content_type}")
                logger.error(f"Response text: {response.text[:500]}")
                return {"error": f"Unexpected response format. Content-Type: {content_type}"}

            try:
                return response.json()
            except ValueError as e:
                logger.error(f"JSON decode error: {e}")
                logger.error(f"Response text: {response.text[:500]}")
                return {"error": f"Invalid JSON response: {str(e)}"}

        except requests.exceptions.RequestException as e:
            logger.error(f"API request failed: {e}")
            return {"error": f"API request failed: {str(e)}"}
        except Exception as e:
            logger.error(f"ID photo creation failed: {e}")
            return {"error": f"ID photo creation failed: {str(e)}"}
    
    def add_background(self,
                      image_input: Union[str, Image.Image, bytes],
                      color: str = "ffffff",
                      kb: Optional[int] = None,
                      render: int = 0,
                      dpi: int = 300) -> Dict[str, Any]:
        """
        添加背景色
        
        Args:
            image_input: 输入图像（RGBA四通道）
            color: 背景色HEX值
            kb: 输出照片的KB值
            render: 渲染模式（0=纯色，1=上下渐变，2=中心渐变）
            dpi: 图像分辨率
            
        Returns:
            Dict[str, Any]: API响应结果
        """
        try:
            data = {
                "color": color,
                "render": render,
                "dpi": dpi,
            }
            
            if kb is not None:
                data["kb"] = kb
            
            # 优先使用文件上传，避免base64编码问题
            if isinstance(image_input, str):
                # 直接使用文件路径上传
                with open(image_input, 'rb') as f:
                    files = {"input_image": f}
                    response = self._make_request("POST", "/add_background", files=files, data=data)
            else:
                # 对于非文件路径输入，先保存为临时文件再上传
                temp_file = None
                try:
                    # 创建临时文件
                    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')

                    if isinstance(image_input, Image.Image):
                        # PIL图像对象
                        image_input.save(temp_file.name, format='PNG')
                    elif isinstance(image_input, bytes):
                        # 字节数据
                        temp_file.write(image_input)
                        temp_file.close()
                    else:
                        raise ValueError("不支持的图像输入类型")

                    # 使用临时文件上传
                    with open(temp_file.name, 'rb') as f:
                        files = {"input_image": f}
                        response = self._make_request("POST", "/add_background", files=files, data=data)

                finally:
                    # 清理临时文件
                    if temp_file and os.path.exists(temp_file.name):
                        os.unlink(temp_file.name)
            
            response.raise_for_status()

            # 检查响应内容类型
            content_type = response.headers.get('content-type', '')
            if 'application/json' not in content_type:
                logger.error(f"Unexpected content type: {content_type}")
                logger.error(f"Response text: {response.text[:500]}")
                return {"error": f"Unexpected response format. Content-Type: {content_type}"}

            try:
                return response.json()
            except ValueError as e:
                logger.error(f"JSON decode error: {e}")
                logger.error(f"Response text: {response.text[:500]}")
                return {"error": f"Invalid JSON response: {str(e)}"}

        except requests.exceptions.RequestException as e:
            logger.error(f"API request failed: {e}")
            return {"error": f"API request failed: {str(e)}"}
        except Exception as e:
            logger.error(f"Add background failed: {e}")
            return {"error": f"Add background failed: {str(e)}"}
    
    def generate_layout_photos(self,
                              image_input: Union[str, Image.Image, bytes],
                              height: int = 413,
                              width: int = 295,
                              kb: Optional[int] = None,
                              dpi: int = 300) -> Dict[str, Any]:
        """
        生成六寸排版照
        
        Args:
            image_input: 输入图像（RGB三通道）
            height: 输入图像的高度
            width: 输入图像的宽度
            kb: 输出照片的KB值
            dpi: 图像分辨率
            
        Returns:
            Dict[str, Any]: API响应结果
        """
        try:
            data = {
                "height": height,
                "width": width,
                "dpi": dpi,
            }
            
            if kb is not None:
                data["kb"] = kb
            
            # 如果是文件路径，使用文件上传
            if isinstance(image_input, str):
                with open(image_input, 'rb') as f:
                    files = {"input_image": f}
                    response = self._make_request("POST", "/generate_layout_photos", files=files, data=data)
            else:
                # 使用base64编码
                data["input_image_base64"] = self._image_to_base64(image_input)
                response = self._make_request("POST", "/generate_layout_photos", data=data)
            
            response.raise_for_status()

            # 检查响应内容类型
            content_type = response.headers.get('content-type', '')
            if 'application/json' not in content_type:
                logger.error(f"Unexpected content type: {content_type}")
                logger.error(f"Response text: {response.text[:500]}")
                return {"error": f"Unexpected response format. Content-Type: {content_type}"}

            try:
                return response.json()
            except ValueError as e:
                logger.error(f"JSON decode error: {e}")
                logger.error(f"Response text: {response.text[:500]}")
                return {"error": f"Invalid JSON response: {str(e)}"}

        except requests.exceptions.RequestException as e:
            logger.error(f"API request failed: {e}")
            return {"error": f"API request failed: {str(e)}"}
        except Exception as e:
            logger.error(f"Generate layout photos failed: {e}")
            return {"error": f"Generate layout photos failed: {str(e)}"}


# 全局API客户端实例
_api_client = None

def get_hivision_client() -> HivisionAPIClient:
    """获取全局API客户端实例"""
    global _api_client
    if _api_client is None:
        _api_client = HivisionAPIClient()
    return _api_client
