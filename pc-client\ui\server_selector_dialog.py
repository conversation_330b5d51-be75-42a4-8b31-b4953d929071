#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
服务端选择对话框
"""

import time
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                             QPushButton, QListWidget, QListWidgetItem,
                             QLineEdit, QGroupBox, QFormLayout, QTabWidget,
                             QWidget, QMessageBox, QProgressBar)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

from core.server_discovery import ServerDiscovery

class ServerSelectorDialog(QDialog):
    """服务端选择对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.selected_server_ip = None
        self.server_discovery = ServerDiscovery()
        self.init_ui()
        self.setup_connections()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("选择服务端")
        self.setFixedSize(500, 400)
        
        layout = QVBoxLayout(self)
        
        # 创建选项卡
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)
        
        # 自动发现选项卡
        discovery_tab = QWidget()
        tab_widget.addTab(discovery_tab, "自动发现")
        self.init_discovery_tab(discovery_tab)
        
        # 手动输入选项卡
        manual_tab = QWidget()
        tab_widget.addTab(manual_tab, "手动输入")
        self.init_manual_tab(manual_tab)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.connect_btn = QPushButton("连接")
        self.connect_btn.setEnabled(False)
        self.connect_btn.clicked.connect(self.accept)
        button_layout.addWidget(self.connect_btn)
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
        
    def init_discovery_tab(self, tab):
        """初始化自动发现选项卡"""
        layout = QVBoxLayout(tab)
        
        # 发现状态
        status_group = QGroupBox("发现状态")
        status_layout = QVBoxLayout(status_group)
        
        self.discovery_status_label = QLabel("未开始发现")
        status_layout.addWidget(self.discovery_status_label)
        
        self.discovery_progress = QProgressBar()
        self.discovery_progress.setRange(0, 0)  # 无限进度条
        self.discovery_progress.setVisible(False)
        status_layout.addWidget(self.discovery_progress)
        
        # 发现控制按钮
        discovery_button_layout = QHBoxLayout()
        
        self.start_discovery_btn = QPushButton("开始发现")
        self.start_discovery_btn.clicked.connect(self.start_discovery)
        discovery_button_layout.addWidget(self.start_discovery_btn)
        
        self.stop_discovery_btn = QPushButton("停止发现")
        self.stop_discovery_btn.setEnabled(False)
        self.stop_discovery_btn.clicked.connect(self.stop_discovery)
        discovery_button_layout.addWidget(self.stop_discovery_btn)
        
        self.refresh_btn = QPushButton("刷新列表")
        self.refresh_btn.clicked.connect(self.refresh_server_list)
        discovery_button_layout.addWidget(self.refresh_btn)
        
        status_layout.addLayout(discovery_button_layout)
        layout.addWidget(status_group)
        
        # 服务端列表
        server_group = QGroupBox("发现的服务端")
        server_layout = QVBoxLayout(server_group)
        
        self.server_list = QListWidget()
        self.server_list.itemClicked.connect(self.on_server_selected)
        self.server_list.itemDoubleClicked.connect(self.on_server_double_clicked)
        server_layout.addWidget(self.server_list)
        
        layout.addWidget(server_group)
        
    def init_manual_tab(self, tab):
        """初始化手动输入选项卡"""
        layout = QVBoxLayout(tab)
        
        # 手动输入组
        manual_group = QGroupBox("手动输入服务端信息")
        manual_layout = QFormLayout(manual_group)
        
        self.manual_ip_input = QLineEdit()
        self.manual_ip_input.setPlaceholderText("例如: *************")
        self.manual_ip_input.textChanged.connect(self.on_manual_ip_changed)
        manual_layout.addRow("服务端IP地址:", self.manual_ip_input)
        
        self.manual_port_input = QLineEdit()
        self.manual_port_input.setText("9090")
        self.manual_port_input.setPlaceholderText("默认: 9090")
        manual_layout.addRow("服务端端口:", self.manual_port_input)
        
        layout.addWidget(manual_group)
        
        # 连接说明
        info_group = QGroupBox("说明")
        info_layout = QVBoxLayout(info_group)
        
        info_text = QLabel("""
手动输入适用于以下情况：
• 服务端在不同网段
• 网络不支持广播
• 需要连接指定的服务端

请确保输入正确的IP地址和端口号。
        """)
        info_text.setWordWrap(True)
        info_layout.addWidget(info_text)
        
        layout.addWidget(info_group)
        layout.addStretch()
        
    def setup_connections(self):
        """设置信号连接"""
        self.server_discovery.server_found.connect(self.on_server_found)
        self.server_discovery.server_lost.connect(self.on_server_lost)
        self.server_discovery.discovery_started.connect(self.on_discovery_started)
        self.server_discovery.discovery_stopped.connect(self.on_discovery_stopped)
        self.server_discovery.error_occurred.connect(self.on_discovery_error)
        
    def start_discovery(self):
        """开始发现服务端"""
        self.server_discovery.start_discovery()
        
    def stop_discovery(self):
        """停止发现服务端"""
        self.server_discovery.stop_discovery()
        
    def refresh_server_list(self):
        """刷新服务端列表"""
        self.server_list.clear()
        servers = self.server_discovery.get_servers()
        
        for server in servers:
            self.add_server_to_list(server)
            
    def add_server_to_list(self, server_info):
        """添加服务端到列表"""
        server_ip = server_info["server_ip"]
        server_port = server_info["server_port"]
        server_name = server_info["server_name"]
        version = server_info["version"]
        last_seen = server_info["last_seen"]
        
        # 计算最后见到的时间
        time_diff = int(time.time() - last_seen)
        if time_diff < 60:
            time_str = f"{time_diff}秒前"
        else:
            time_str = f"{time_diff//60}分钟前"
        
        # 创建列表项
        item_text = f"{server_name} ({server_ip}:{server_port})\n版本: {version} | 最后见到: {time_str}"
        
        item = QListWidgetItem(item_text)
        item.setData(Qt.UserRole, server_ip)
        
        # 设置字体
        font = QFont()
        font.setPointSize(10)
        item.setFont(font)
        
        self.server_list.addItem(item)
        
    def on_server_found(self, server_info):
        """发现服务端回调"""
        self.add_server_to_list(server_info)
        
        # 更新状态
        count = self.server_discovery.get_server_count()
        self.discovery_status_label.setText(f"已发现 {count} 个服务端")
        
    def on_server_lost(self, server_ip):
        """服务端丢失回调"""
        # 从列表中移除
        for i in range(self.server_list.count()):
            item = self.server_list.item(i)
            if item.data(Qt.UserRole) == server_ip:
                self.server_list.takeItem(i)
                break
                
        # 更新状态
        count = self.server_discovery.get_server_count()
        self.discovery_status_label.setText(f"已发现 {count} 个服务端")
        
    def on_discovery_started(self):
        """发现开始回调"""
        self.discovery_status_label.setText("正在发现服务端...")
        self.discovery_progress.setVisible(True)
        self.start_discovery_btn.setEnabled(False)
        self.stop_discovery_btn.setEnabled(True)
        
    def on_discovery_stopped(self):
        """发现停止回调"""
        count = self.server_discovery.get_server_count()
        self.discovery_status_label.setText(f"发现已停止，共发现 {count} 个服务端")
        self.discovery_progress.setVisible(False)
        self.start_discovery_btn.setEnabled(True)
        self.stop_discovery_btn.setEnabled(False)
        
    def on_discovery_error(self, error_message):
        """发现错误回调"""
        self.discovery_status_label.setText(f"发现失败: {error_message}")
        self.discovery_progress.setVisible(False)
        self.start_discovery_btn.setEnabled(True)
        self.stop_discovery_btn.setEnabled(False)
        
    def on_server_selected(self, item):
        """服务端选择回调"""
        self.selected_server_ip = item.data(Qt.UserRole)
        self.connect_btn.setEnabled(True)
        
    def on_server_double_clicked(self, item):
        """服务端双击回调"""
        self.selected_server_ip = item.data(Qt.UserRole)
        self.accept()
        
    def on_manual_ip_changed(self, text):
        """手动IP输入变化回调"""
        if text.strip():
            self.selected_server_ip = text.strip()
            self.connect_btn.setEnabled(True)
        else:
            self.selected_server_ip = None
            self.connect_btn.setEnabled(False)
            
    def get_selected_server(self):
        """获取选中的服务端"""
        if self.selected_server_ip:
            # 检查是否是手动输入的IP（与手动输入框的内容一致）
            manual_ip = self.manual_ip_input.text().strip() if hasattr(self, 'manual_ip_input') else ""

            if manual_ip and manual_ip == self.selected_server_ip:
                # 手动输入的服务端
                port = 9090
                try:
                    port_text = self.manual_port_input.text().strip()
                    if port_text:
                        port = int(port_text)
                except:
                    pass
                return {
                    "server_ip": self.selected_server_ip,
                    "server_port": port,
                    "server_name": f"手动输入 ({self.selected_server_ip}:{port})",
                    "manual": True
                }
            else:
                # 从发现列表中查找
                servers = self.server_discovery.get_servers()
                for server in servers:
                    if server["server_ip"] == self.selected_server_ip:
                        server["manual"] = False
                        return server

        return None
        
    def closeEvent(self, event):
        """关闭事件"""
        self.server_discovery.stop_discovery()
        event.accept()
