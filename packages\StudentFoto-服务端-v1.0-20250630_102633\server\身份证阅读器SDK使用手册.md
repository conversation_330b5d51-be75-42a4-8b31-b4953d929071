﻿` `**![](Aspose.Words.42697722-af20-4995-bbdc-4902e6d236a4.001.jpeg "CV")**                                  身份证阅读器SDK使用手册





**身份证阅读器SDK使用手册**

**V3.3.0.7**








**深圳华视电子读写设备有限公司**

**2018年08月20日**








版本信息：

|序号|版本号|修改日期|修改内容|
| :-: | :-: | :-: | :-: |
|1|V3.3.0.1|2016-09-20|在上一版本的基础上，修复多出动态库BUG，重新撰写使用说明。|
|2|V3.3.0.2|2016-12-19|增加对穿青人和达斡尔族民族的显示|
|3|V3.3.0.3|2017-06-23|增加对外国人永居证的读取|
|4|V3.3.0.4|2017-08-08|增加对革家人民族的显示|
|5|V3.3.0.5|2017-11-09|增加对windows64位系统的支持|
|6|V3.3.0.6|2018-07-03|增加PB语言的示例代码|
|7|V3.3.0.7|2018-08-20|增加对港澳台居民居住证的读取|
|8|V3.3.0.8|2023-09-26|增加新版外国人|











# **目  录**
[一、文件说明	4]()

[二、函数列表	5]()

[1.居民身份证结构：	6]()

[2.外国人居留证结构：	7]()

[3.港澳台居住证结构：	8]()

[三、函数详细说明	9]()

[四、函数调用流程	15]()

[五、函数说明	16]()

[1.初始化连接	16]()

[2.关闭端口	17]()

[3.卡认证	17]()

[4.读卡操作	18]()

[5.获取性别代码	19]()

[6.获取民族代码	19]()

[六、信息格式说明	20]()

[1.居民身份证txt文件格式	20]()

[2.外国人永居证txt文件格式	20]()

[3.港澳台居民居住证TXT文件格式	21]()

[七、其他函数用法	22]()



**

`	`本手册是操作身份证阅读器动态库应用函数的定义格式、调用方法和返回值的说明。
# <a name="_toc522701102"></a>**一、文件说明**
应用函数开发包含下列文件：

termb.dll 		 API函数的动态联接库

sdtapi.dll     安全模块通讯函数

WltRs.dll 		 身份证相片解码库

SysInfo.dll    

适用操作系统：

`    `Windows XP、Windows7（32位和64位）等常见操作系统；

适用开发语言：

`	`Visual C++   6.0  及以后版本

`    `Visual Basic 6.0  及以后版本

`    `Delphi        6.0 及以后版本

`    `PowerBuilder 6.0  及以后版本

`    `visual C# 2005    及以后版本

开发联系人:13723716489 何工



21/22
# <a name="_toc522701103"></a>**二、函数列表**
`	`//以下为主要API函数

int CVR\_InitComm(int Port)       						 初始化连接;

int CVR\_Authenticate()									 卡认证;

int CVR\_AuthenticateForNoJudge()						 卡认证一直成功版本

int CVR\_Read\_Content(int active)		    			 读卡操作。

int CVR\_Read\_FPContent()		    	 	    			 读卡操作，含指纹。

int CVR\_CloseComm()			      						 关闭连接;

int CVR\_FindCard ()			      						 找卡

int CVR\_SelectCard()			      				   	     选卡


## <a name="_toc522701104"></a>**1.居民身份证结构：**

|**名称**|**含义**|**长度**|**备注**|
| :-: | :-: | :-: | :-: |
|姓名|姓名|不超过30字节||
|性别|性别|不超过2个字节，符合GB/T 2261.1的规定||
|民族|民族|不超过20个字节，或10个unicode字符||
|出生日期|出生日期|不超过16个字节，格式为YYMMDD||
|住址|户口所在地|不超过70个字节，或35个unicode字符||
|公民身份证号码|证件号码|不超过36个字节，或18个unicode字符||
|签发机关|签发机关|不超过30字节||
|有效期起始日期|有效期起始日期|不超过16个字节，格式为YYMMDD||
|有效期终止日期|有效期终止日期|不超过16个字节，格式为YYMMDD||
|``|<p></p><p></p>|||
|指纹数据|指纹数据|不超过1024字节，二进制数据|需证件内含有指纹|

## <a name="_toc522701105"></a>**2.外国人居留证结构：**

|**名称**|**含义**|**长度**|**备注**|
| :-: | :-: | :-: | :-: |
|英文姓名|外国人英文姓名|不超过120字节||
|性别|外国人性别|不超过2个字节，符合GB/T 2261.1的规定||
|永久居留证号码|证件号码|不超过30个字节，或15个unicode字符||
|国籍、地区代码|国籍或所在地区代码|参考GB/T 2659-2000中文缩写||
|中文姓名|中文姓名|不超过30个字节，或15个unicode双字节(UTF16)字符||
|证件签发日期|证件签发日期|不超过16个字节，格式为YYMMDD||
|证件终止日期|证件终止日期|不超过16个字节，格式为YYMMDD||
|出生日期|出生日期|不超过16个字节，格式为YYMMDD||
|证件版本号|证件版本号|不超过4字节||
|受理机关代码|当次申请受理机关代码|不超过8个字节||
|证件类别|证件类别|“I”||
|照片内容|照片内容|若取得bmp数据，则不超过38862字节，jpg数据长度可变||
## <a name="_toc522701106"></a>**3.港澳台居住证结构：**

|**名称**|**含义**|**长度**|**备注**|
| :-: | :-: | :-: | :-: |
|姓名|姓名|不超过30字节||
|性别|性别|不超过2个字节，符合GB/T 2261.1的规定||
|出生日期|出生日期|不超过16个字节，格式为YYMMDD||
|住址|户口所在地|不超过70个字节，或35个unicode字符||
|公民身份证号码|证件号码|不超过36个字节，或18个unicode字符||
|签发机关|签发机关|不超过30字节||
|有效期起始日期|有效期起始日期|不超过16个字节，格式为YYMMDD||
|有效期终止日期|有效期终止日期|不超过16个字节，格式为YYMMDD||
|通行证号码|通行证号码|不超过18个字节||
|签发次数|签发次数|不超过4字节||
|证件类别|证件类别|“J”||
|照片内容|照片内容|若取得bmp数据，则不超过38862字节，jpg数据长度可变||
|指纹数据|指纹数据|不超过1024字节，2进制数据||


## **4.新版外国人居留证结构：**

|**名称**|**含义**|**长度**|**备注**|
| :-: | :-: | :-: | :-: |
|英文姓名|外国人英文姓名|不超过92字节||
|性别|外国人性别|不超过2个字节，符合GB/T 2261.1的规定||
|永久居留证号码|证件号码|不超过30个字节，或15个unicode字符||
|国籍、地区代码|国籍或所在地区代码|参考GB/T 2659-2000中文缩写||
|中文姓名|中文姓名|不超过30个字节，或15个unicode双字节(UTF16)字符||
|证件签发日期|证件签发日期|不超过16个字节，格式为YYMMDD||
|证件终止日期|证件终止日期|不超过16个字节，格式为YYMMDD||
|出生日期|出生日期|不超过16个字节，格式为YYMMDD||
|换证次数|换证次数|4字节||
|旧版证件号码|旧版证件号码|6字节||
|证件类别|证件类别|“Y”||
|照片内容|照片内容|若取得bmp数据，则不超过38862字节，jpg数据长度可变||

**注：CVR\_Authenticate功能和CVR\_FindCard+ CVR\_SelectCard一样，CVR\_Authenticate内部调用了找卡和选卡函数，接口分开是为了调用者开发方便。**

# <a name="_toc522701107"></a>**三、函数详细说明**
//以下为可选API函数,方便二次开发，多字节版本

红色字体代表读取到的身份证信息

蓝色字体代表读取到的外国人永久居留身份证信息

绿色字体代表读取到的港澳台居民居住证信息

紫色字体代表新版外国人

`	`**int  GetPeopleName(char \*strTmp, int \*strLen)**	    			 	

得到姓名信息

得到英文姓名信息	

得到姓名信息

紫色字体代表新版外国人

`	`**int  GetPeopleSex(char \*strTmp, int \*strLen)**	    			 	

得到性别信息

得到性别信息

得到性别信息

得到性别信息

`	`**int  GetPeopleNation(char \*strTmp, int \*strLen)**	    			 

得到民族信息

得到国籍信息

得到国籍信息

`	`**int  GetPeopleBirthday(char \*strTmp, int \*strLen)**			 	

得到出生日期

得到出生日期

得到出生日期

得到出生日期

`	`**int  GetPeopleIDCode(char \*strTmp, int \*strLen)**			 	

得到身份证号信息

得到永久居证号码

得到公民身份号码

得到永久居证号码

`	`**int  GetDepartment(char \*strTmp, int \*strLen)**	    			 	

得到发证机关信息

得到档次受理申请机关代码

得到签发机关信息

`	`**int  GetStartDate(char \*strTmp, int \*strLen)**	    				

得到有效开始日期

得到有效开始日期

得到有效开始日期

得到有效开始日期

`	`**int  GetEndDate(char \*strTmp, int \*strLen)**	        				

得到有效截止日期

得到有效截止日期

得到有效截止日期

得到有效截止日期

**int GetNationCode(unsigned char \* nationData, int \*strLen)**	

得到居民民族代码

得到外国人国籍代码

得到外国人国籍代码

**int GetSexCode(unsigned char \* sexData, int \* pLen)**				

得到性别代码

得到性别代码

得到性别代码

得到性别代码

**int GetCertType (unsigned char \* nationData, int \*pLen)**

获取证件类别

(身份证返回值为空，外国人永居证返回值为“I”,港澳台居民居住证返回值为“J”,新版外国人“Y”)

**int  GetFPDate (unsigned char \*pData, int \* pLen)**			    

得到指纹数据，不超过1024字节

得到指纹数据，不超过1024字节

**int  GetPeopleAddress (char \*strTmp, int \*strLen)**			 	

得到地址信息

得到地址信息

**int GetPassCheckID( unsigned char \*strTmp, int \*strLen);**        

通行证号码，18字节

**int WINAPI GetIssuesNum(int \*IssuesNum);**               

签发次数，4字节

发证次数，4字节

**int GetNewAppMsg (unsigned char \* nationData, int \*pLen)**

获取追加地址

**int  GetBMPData (unsigned char \*pData, int \* pLen)**			    

得到头像照片bmp数据，不超过38862字节

**int  Getbase64BMPData (unsigned char \*pData, int \* pLen)**	    

得到头像照片base64编码数据，不超过38862\*2字节

**int  Getbase64JpgData (unsigned char \*pData, int \* pLen)**			

得到头像照片jpg数据，不超过38862字节

**int GetJpgData(unsigned char \* jpgData, int \* pLen)**				

获取jpg二进制数据

**int  GetPeopleChineseName( unsigned char \*strTmp, int \*strLen)**	

获取外国人中文姓名

获取外国人中文姓名

**int  GetOldIDCardNumber( unsigned char \*strTmp, int \*strLen)**	

获取旧版外国人证件号码

**int  GetStandbyEName( unsigned char \*strTmp, int \*strLen)**	

获取英文备用名

**int  GetPeopleCertVersion( unsigned char \*strTmp, int \*strLen)**	

得到外国人证件版本

**int  CVR\_GetSAMID(char \*SAMID)**	        				    

得到安全模块号

//以下为可选API函数,方便二次开发，传入参数为unicode字符地址

红色字体代表读取到的身份证信息

蓝色字体代表读取到的外国人永久居留身份证信息

绿色字体代表读取到的港澳台居民居住证信息

`	`**int  GetPeopleNameU(char \*strTmp, int \*strLen)**	 

得到姓名信息

得到英文姓名信息

得到姓名信息

得到英文姓名信息

`	`**int  GetPeopleSexU (char \*strTmp, int \*strLen)**	

得到性别信息

得到性别信息

得到性别信息

得到性别信息

`	`**int  GetPeopleNationU (char \*strTmp, int \*strLen)**	    			

得到民族信息

得到国籍信息

得到国籍信息

`	`**int  GetPeopleBirthdayU (char \*strTmp, int \*strLen)**			 	

得到出生日期

得到出生日期

得到出生日期

得到出生日期

`	`**int  GetPeopleIDCodeU (char \*strTmp, int \*strLen)**			 	

得到身份证号信息

得到永居证号码

得到公民身份号码

得到永居证号码

`	`**int  GetDepartmentU (char \*strTmp, int \*strLen)**	    			 

得到发证机关信息

得到档次受理申请机关代码

得到签发机关信息

`	`**int  GetStartDateU (char \*strTmp, int \*strLen)**  					

得到有效开始日期

得到有效开始日期

得到有效开始日期

得到有效开始日期

`	`**int  GetEndDateU (char \*strTmp, int \*strLen)**      				

得到有效截止日期

得到有效截止日期

得到有效截止日期

得到有效截止日期

**int GetNationCodeU (unsigned char \* nationData, int \*pLen)**

得到居民民族代码

得到外国人国籍代码

得到外国人国籍代码

**int GetSexCodeU(unsigned char \* sexData, int \* pLen)**			

得到性别代码

得到性别代码

得到性别代码

得到性别代码

**int GetCertTypeU (unsigned char \* nationData, int \*pLen)**	

获取证件类别

(身份证返回值为空，外国人永居证返回值为“I”,港澳台居民居住证返回值为“J”，新版外国人“Y”)

**int  GetPeopleAddressU (char \*strTmp, int \*strLen)**

得到地址信息

得到地址信息

**int  GetFPDate (unsigned char \*pData, int \* pLen)**			    

得到指纹数据，不超过1024字节

得到指纹数据，不超过1024字节

**int GetPassCheckIDU( unsigned char \*strTmp, int \*strLen);**        

通行证号码，18字节

**int WINAPI GetIssuesNumU(int \*IssuesNum);**               

签发次数，4字节

发证次数，4字节

**int GetNewAppMsg U(unsigned char \* nationData, int \*pLen)**

获取追加地址

**int  GetBMPData (unsigned char \*pData, int \* pLen)**			    

得到头像照片bmp数据，不超过38862字节

**int  Getbase64BMPDataU (unsigned char \*pData, int \* pLen)**	    

得到头像照片base64编码，不超过38862\*4字节

**int  Getbase64JpgData U (unsigned char \*pData, int \* pLen)**		

得到头像照片jpg的base64编码，不超过38862\*2字节

**int GetJpgData(unsigned char \* jpgData, int \* pLen)**				

获取jpg二进制数据

**int  GetPeopleChineseNameU( unsigned char \*strTmp, int \*strLen)**

得到外国人中文姓名

得到外国人中文姓名

**int GetPeopleCertVersionU( unsigned char \*strTmp, int \*strLen)**	

得到外国人证件版本

**int  GetOldIDCardNumberU( unsigned char \*strTmp, int \*strLen)**	

获取旧版外国人证件号码

**int  GetStandbyENameU( unsigned char \*strTmp, int \*strLen)**	

获取英文备用名

**int  CVR\_GetSAMIDU (char \*SAMID)**	        			    

得到安全模块号
# <a name="_toc522701108"></a>**四、函数调用流程**
CVR\_InitComm

CVR\_CloseComm

CVR\_Authenticate

返回1

GetPeopleName

CVR\_Read\_Conten

返回1

返回1

是

否

是

否

GetPeopleSex

GetPeopleNation

否

是
![](Aspose.Words.42697722-af20-4995-bbdc-4902e6d236a4.002.png)

# <a name="_toc522701109"></a>**五、函数说明**
## <a name="_toc522701110"></a>**1.初始化连接**
原    型：int CVR\_InitComm (int Port)

说    明：本函数用于PC与华视电子第二代居民身份证阅读器的连接。

参    数：Port：连接串口（COM1~COM16）或USB口(1001~1016)

|参数名|含义|取值范围|
| :-: | :-: | :-: |
|int Port|端口编号|见下表|

端口编号：

|值|意义|
| :-: | :-: |
|1|串口1|
|2|串口2|
|3|串口3|
|...|...|
|16|串口16|
|||
|1001|USB口1|
|1002|USB口2|
|1003|USB口3|
|...|...|
|1016|USB口16|

返 回 值：

|值|意义|
| :-: | :-: |
|1|正确|
|2|端口打开失败|
|-1|未知错误|
|-2|动态库加载失败|
## <a name="_toc522701111"></a>**2.关闭端口**
原    型：

` 		`int CVR\_CloseComm(void)

说    明：本函数用于关闭PC到阅读器的连接。

参    数：无

返 回 值：

|值|意义|
| :-: | :-: |
|1|关闭成功|
|0|端口号不合法|
|-1|端口已经关闭|
|-2|动态库加载失败|

## <a name="_toc522701112"></a>**3.卡认证**
原    型：int CVR\_Authenticate (void)

说    明：本函数用于读卡器和卡片之间的合法身份确认。卡认证循环间隔大于300ms。

参    数： 

返 回 值：

|值|意义|说明|
| :-: | :-: | :-: |
|1|正确|卡片认证成功|
|2|错误|寻卡失败|
|3|错误|选卡失败|
|4|错误|未连接读卡器|
|0|错误|动态库未加载|

**（若卡片放置后发生认证错误时，请移走卡片重新放置。）**

原    型：int CVR\_AuthenticateForNoJudge (void)

说    明：使用此接口读卡不用每次都拿起放下，可以一直读。此函数返回值一直为1。
4. ## <a name="_toc522701113"></a>**读卡操作**
   原    型：CVR\_Read\_FPContent()；	 

   说    明：本函数用于通过阅读器从第二代居民身份证中读取相应信息。卡认证成功以后才可做读卡操作，读卡完毕若继续读卡应移走二代证卡片重新放置做卡认证。

   参    数： 无

   返 回 值：

   |返回值|意义|
   | :-: | :-: |
   |1|正确|
   |0|错误，读身份证失败|
   |4|错误，身份证读卡器未连接|
   |99|动态库未加载|

    
   ## <a name="_toc522701114"></a>**5.获取性别代码**
   原    型：int GetSexCode(unsigned char \* sexData, int \* pLen)

   说    明：本函数获取性别代码

   参    数： 

   |参数名|含义|取值范围|
   | :-: | :-: | :-: |
   |unsigned char \* sexData|缓冲区地址||
   |int \* pLen|缓冲区长度指针|4字节|

   返 回 值：

   |值|意义|
   | :-: | :-: |
   |1|正确|
   |0|错误|

   ## <a name="_toc522701115"></a>**6.获取民族代码**
   原    型：int GetNationCode(unsigned char \* nationData, int \* pLen);

   说    明：本函数获取民族代码

   参    数：

   |参数名|含义|取值范围|
   | :-: | :-: | :-: |
   |unsigned char \* nationData|缓冲区地址||
   |int \* pLen|缓冲区长度指针|4字节|

   返 回 值：

   |值|意义|
   | :-: | :-: |
   |1|正确|
   |0|错误|

7. ## **获取设备状态**
   原    型：int CVR\_GetStatus();

   说    明：本函数获取设备状态

   返 回 值：

   |值|意义|
   | :-: | :-: |
   |1|状态正常|
   |-1|状态异常|


   # <a name="_toc522701116"></a>**六、信息格式说明**
   ## <a name="_toc522701117"></a>**1.居民身份证txt文件格式**
   读卡成功后在当前目录下生成wz.txt（文字信息）和zp.bmp（照片信息）

   wz.txt内容示例如下：

   张红叶

   女

   汉

   19881118

   河北省邯郸市临漳县称勾镇称勾东村复兴路25号

   130423198811184328

   临漳县公安局

   20110330-20210330

   ## <a name="_toc522701118"></a>**2.外国人永居证txt文件格式**
   读卡成功后在当前目录下生成wz.txt（文字信息）和zp.bmp（照片信息）

   wz.txt内容示例如下：

   ZHENGJIAN,YANGBEN

   `	`证件样本

   `	`女

   `	`19810803

   `	`加拿大

   `	`CAN

   `	`20151025-20251024

   `	`CAN110081080319

   `	`1500

   `	`01

   `	`从上到下依次为：英文姓名，中文姓名，性别，出生日期，国籍，国籍代码，有效期限，证件号码，签发机关代码，证件版本号。

   ## <a name="_toc522701119"></a>**3.港澳台居民居住证TXT文件格式**
   读卡成功后在当前目录下生成wz.txt（文字信息）和zp.bmp（照片信息）

   wz.txt内容示例如下：

   `    `金鑫

   `    `女

   `    `19940823

   `    `北京市西城区复兴门外大街999号院11号楼3单元502室

   `    `810000199408230013

   `    `北京市公安局西城分局

   `    `20171027-20221026

   `    `000000000

   1

   ## **4.新版外国人永局证TXT文件格式**
   ZHENGJIAN, YANGBEN ZHENG JIAN YANG

   证件样本证件样本

   女

   2016-01-01

   巴基斯坦

   PAK

   20230808-20280807

   931586201601010026

   BEN

   0

# <a name="_toc522701120"></a>**七、其他函数用法**
读各项文字信息到自定义内存缓冲

原  型：

`	`int  GetPeopleName(char \*strTmp, int \*strLen)	    //得到姓名信息

`	`int  GetPeopleSex(char \*strTmp, int \*strLen)	    //得到性别信息

`	`int  GetPeopleNation(char \*strTmp, int \*strLen)	    //得到民族信息	

`	`int  GetPeopleBirthday(char \*strTmp, int \*strLen)	//得到出生日期	

`	`int  GetPeopleAddress(char \*strTmp, int \*strLen)	//得到地址信息	

`	`int  GetPeopleIDCode(char \*strTmp, int \*strLen)	//得到卡号信息	

`	`int  GetDepartment(char \*strTmp, int \*strLen)	    //得到发证机关信息	

`	`int  GetStartDate(char \*strTmp, int \*strLen)	        //得到有效开始日期	

`	`int  GetEndDate(char \*strTmp, int \*strLen)	        //得到有效截止日期

int  CVR\_GetSAMID(char \* SAMID)            //得到安全模块号码

参数：

`       `\*strTmp   返回的信息缓存指针。

`       `\*strLen    返回的信息长度指针。

返 回 值：

|返回值|意义|
| :-: | :-: |
|1|正确|
|0|错误|

**注意：若采用查询方式自动判断卡片是否放置，则间隔时间建议大于600ms。**

