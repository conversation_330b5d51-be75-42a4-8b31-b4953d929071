# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[('id_card_bridge.exe', '.'), ('server\\DLL_File.dll', 'server'), ('server\\Termb.dll', 'server'), ('server\\WltRS.dll', 'server'), ('server\\sdtapi.dll', 'server')],
    datas=[('ui', 'ui'), ('core', 'core'), ('config.py', '.'), ('server\\license.dat', 'server'), ('server\\身份证阅读器SDK使用手册.md', 'server')],
    hiddenimports=['PyQt5.QtCore', 'PyQt5.QtGui', 'PyQt5.QtWidgets', 'sqlite3'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='StudentFoto-服务端',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
