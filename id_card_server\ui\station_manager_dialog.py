#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机位号管理对话框
用于查看和手动调整机位号分配
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                             QPushButton, QTableWidget, QTableWidgetItem,
                             QHeaderView, QSpinBox, QMessageBox, QGroupBox,
                             QFormLayout, QLineEdit, QTextEdit)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
import logging

logger = logging.getLogger(__name__)

class StationManagerDialog(QDialog):
    """机位号管理对话框"""
    
    def __init__(self, task_manager, parent=None):
        super().__init__(parent)
        self.task_manager = task_manager
        self.init_ui()
        self.load_station_data()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("机位号管理")
        self.setFixedSize(800, 600)
        
        layout = QVBoxLayout(self)
        
        # 说明标签
        info_label = QLabel("机位号分配管理 - 基于机器码自动分配，支持手动调整")
        info_label.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        info_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(info_label)
        
        # 当前连接的客户端
        current_group = QGroupBox("当前连接的客户端")
        current_layout = QVBoxLayout(current_group)
        
        self.current_table = QTableWidget()
        self.current_table.setColumnCount(5)
        self.current_table.setHorizontalHeaderLabels([
            "客户端ID", "机器码", "IP地址", "当前机位号", "操作"
        ])
        
        # 设置表格属性
        header = self.current_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        
        self.current_table.setAlternatingRowColors(True)
        self.current_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        current_layout.addWidget(self.current_table)
        layout.addWidget(current_group)
        
        # 机器码历史记录
        history_group = QGroupBox("机器码历史记录")
        history_layout = QVBoxLayout(history_group)
        
        self.history_table = QTableWidget()
        self.history_table.setColumnCount(3)
        self.history_table.setHorizontalHeaderLabels([
            "机器码", "分配机位号", "操作"
        ])
        
        # 设置表格属性
        header = self.history_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        
        self.history_table.setAlternatingRowColors(True)
        self.history_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        history_layout.addWidget(self.history_table)
        layout.addWidget(history_group)
        
        # 手动分配区域
        manual_group = QGroupBox("手动分配机位号")
        manual_layout = QFormLayout(manual_group)
        
        self.machine_id_input = QLineEdit()
        self.machine_id_input.setPlaceholderText("输入机器码")
        manual_layout.addRow("机器码:", self.machine_id_input)
        
        self.station_spin = QSpinBox()
        self.station_spin.setRange(1, 99)
        self.station_spin.setValue(1)
        manual_layout.addRow("机位号:", self.station_spin)
        
        manual_btn_layout = QHBoxLayout()
        
        self.assign_btn = QPushButton("分配机位号")
        self.assign_btn.clicked.connect(self.manual_assign_station)
        self.assign_btn.setStyleSheet("background-color: #3498db; color: white; padding: 5px;")
        manual_btn_layout.addWidget(self.assign_btn)
        
        self.clear_btn = QPushButton("清除分配")
        self.clear_btn.clicked.connect(self.clear_assignment)
        self.clear_btn.setStyleSheet("background-color: #e74c3c; color: white; padding: 5px;")
        manual_btn_layout.addWidget(self.clear_btn)
        
        manual_layout.addRow("", manual_btn_layout)
        layout.addWidget(manual_group)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.load_station_data)
        self.refresh_btn.setStyleSheet("background-color: #27ae60; color: white; padding: 8px;")
        button_layout.addWidget(self.refresh_btn)
        
        button_layout.addStretch()
        
        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.accept)
        self.close_btn.setStyleSheet("background-color: #95a5a6; color: white; padding: 8px;")
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
        
    def load_station_data(self):
        """加载机位号数据"""
        try:
            # 加载当前连接的客户端
            self.load_current_clients()
            
            # 加载历史记录
            self.load_history_records()
            
        except Exception as e:
            logger.error(f"加载机位号数据失败: {e}")
            QMessageBox.critical(self, "加载失败", f"加载数据时发生错误：\n{str(e)}")
    
    def load_current_clients(self):
        """加载当前连接的客户端"""
        try:
            clients_info = self.task_manager.get_connected_clients_info()
            
            self.current_table.setRowCount(len(clients_info))
            
            for row, (client_id, info) in enumerate(clients_info.items()):
                # 获取客户端详细信息
                client_data = self.task_manager.connected_clients.get(client_id, {})
                machine_id = client_data.get('machine_id', '未知')
                address = info.get("address", "未知")
                station_number = info.get("station_number", 1)
                
                # 填充表格
                self.current_table.setItem(row, 0, QTableWidgetItem(client_id))
                self.current_table.setItem(row, 1, QTableWidgetItem(machine_id))
                self.current_table.setItem(row, 2, QTableWidgetItem(address))
                self.current_table.setItem(row, 3, QTableWidgetItem(str(station_number)))
                
                # 添加修改按钮
                modify_btn = QPushButton("修改")
                modify_btn.setStyleSheet("background-color: #f39c12; color: white; padding: 3px;")
                modify_btn.clicked.connect(lambda checked, cid=client_id, mid=machine_id: self.modify_client_station(cid, mid))
                self.current_table.setCellWidget(row, 4, modify_btn)
                
        except Exception as e:
            logger.error(f"加载当前客户端失败: {e}")
    
    def load_history_records(self):
        """加载历史记录"""
        try:
            machine_stations = self.task_manager.machine_stations
            
            self.history_table.setRowCount(len(machine_stations))
            
            for row, (machine_id, station_number) in enumerate(machine_stations.items()):
                self.history_table.setItem(row, 0, QTableWidgetItem(machine_id))
                self.history_table.setItem(row, 1, QTableWidgetItem(str(station_number)))
                
                # 添加删除按钮
                delete_btn = QPushButton("删除")
                delete_btn.setStyleSheet("background-color: #e74c3c; color: white; padding: 3px;")
                delete_btn.clicked.connect(lambda checked, mid=machine_id: self.delete_machine_record(mid))
                self.history_table.setCellWidget(row, 2, delete_btn)
                
        except Exception as e:
            logger.error(f"加载历史记录失败: {e}")
    
    def modify_client_station(self, client_id: str, machine_id: str):
        """修改客户端机位号"""
        try:
            # 预填充当前信息
            self.machine_id_input.setText(machine_id)
            current_station = self.task_manager.get_client_station_number(client_id)
            self.station_spin.setValue(current_station)
            
            QMessageBox.information(self, "修改机位号", 
                                  f"请在下方手动分配区域修改机器码 {machine_id} 的机位号")
            
        except Exception as e:
            logger.error(f"修改客户端机位号失败: {e}")
            QMessageBox.critical(self, "修改失败", f"修改时发生错误：\n{str(e)}")
    
    def manual_assign_station(self):
        """手动分配机位号"""
        try:
            machine_id = self.machine_id_input.text().strip()
            station_number = self.station_spin.value()
            
            if not machine_id:
                QMessageBox.warning(self, "输入错误", "请输入机器码")
                return
            
            # 检查机位号是否已被使用
            for mid, snum in self.task_manager.machine_stations.items():
                if snum == station_number and mid != machine_id:
                    reply = QMessageBox.question(self, "机位号冲突", 
                                                f"机位号 {station_number} 已被机器码 {mid} 使用，是否覆盖？")
                    if reply != QMessageBox.Yes:
                        return
                    # 删除旧的分配
                    del self.task_manager.machine_stations[mid]
                    break
            
            # 分配机位号
            self.task_manager.machine_stations[machine_id] = station_number

            # 更新当前连接的客户端
            for client_id, client_data in self.task_manager.connected_clients.items():
                if client_data.get('machine_id') == machine_id:
                    client_data['station_number'] = station_number
                    self.task_manager.client_stations[client_id] = station_number

            # 保存分配记录
            self.task_manager.save_station_assignments()

            QMessageBox.information(self, "分配成功",
                                  f"已将机器码 {machine_id} 分配到 {station_number} 号机位")
            
            # 刷新显示
            self.load_station_data()
            
        except Exception as e:
            logger.error(f"手动分配机位号失败: {e}")
            QMessageBox.critical(self, "分配失败", f"分配时发生错误：\n{str(e)}")
    
    def clear_assignment(self):
        """清除分配"""
        try:
            machine_id = self.machine_id_input.text().strip()
            
            if not machine_id:
                QMessageBox.warning(self, "输入错误", "请输入要清除的机器码")
                return
            
            if machine_id not in self.task_manager.machine_stations:
                QMessageBox.warning(self, "清除失败", "该机器码没有分配记录")
                return
            
            reply = QMessageBox.question(self, "确认清除", 
                                       f"确定要清除机器码 {machine_id} 的机位号分配吗？")
            if reply == QMessageBox.Yes:
                del self.task_manager.machine_stations[machine_id]
                # 保存分配记录
                self.task_manager.save_station_assignments()
                QMessageBox.information(self, "清除成功", "机位号分配已清除")
                
                # 刷新显示
                self.load_station_data()
                
        except Exception as e:
            logger.error(f"清除分配失败: {e}")
            QMessageBox.critical(self, "清除失败", f"清除时发生错误：\n{str(e)}")
    
    def delete_machine_record(self, machine_id: str):
        """删除机器记录"""
        try:
            reply = QMessageBox.question(self, "确认删除", 
                                       f"确定要删除机器码 {machine_id} 的记录吗？")
            if reply == QMessageBox.Yes:
                if machine_id in self.task_manager.machine_stations:
                    del self.task_manager.machine_stations[machine_id]
                    # 保存分配记录
                    self.task_manager.save_station_assignments()
                    QMessageBox.information(self, "删除成功", "记录已删除")
                    
                    # 刷新显示
                    self.load_station_data()
                    
        except Exception as e:
            logger.error(f"删除机器记录失败: {e}")
            QMessageBox.critical(self, "删除失败", f"删除时发生错误：\n{str(e)}")
