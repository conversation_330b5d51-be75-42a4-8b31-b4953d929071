"""
设备发现服务
用于监听Android设备的UDP广播，自动发现局域网内的设备
"""

import socket
import json
import threading
import time
from typing import Dict, List, Callable, Optional
import logging

logger = logging.getLogger(__name__)

class DeviceInfo:
    """设备信息类"""
    def __init__(self, device_name: str, device_ip: str, tcp_port: int, timestamp: int):
        self.device_name = device_name
        self.device_ip = device_ip
        self.tcp_port = tcp_port
        self.timestamp = timestamp
        self.last_seen = time.time()
    
    def update_last_seen(self):
        """更新最后发现时间"""
        self.last_seen = time.time()
    
    def is_expired(self, timeout: int = 30) -> bool:
        """检查设备是否过期（超过指定时间未发现）"""
        return time.time() - self.last_seen > timeout
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'device_name': self.device_name,
            'device_ip': self.device_ip,
            'tcp_port': self.tcp_port,
            'timestamp': self.timestamp,
            'last_seen': self.last_seen
        }
    
    def __str__(self):
        return f"{self.device_name} ({self.device_ip}:{self.tcp_port})"

class DeviceDiscoveryService:
    """设备发现服务"""
    
    def __init__(self, broadcast_port: int = 8081):
        self.broadcast_port = broadcast_port
        self.devices: Dict[str, DeviceInfo] = {}  # key: device_ip
        self.running = False
        self.socket = None
        self.discovery_thread = None
        self.cleanup_thread = None
        
        # 回调函数
        self.on_device_found: Optional[Callable[[DeviceInfo], None]] = None
        self.on_device_lost: Optional[Callable[[DeviceInfo], None]] = None
        self.on_device_updated: Optional[Callable[[DeviceInfo], None]] = None
    
    def start(self):
        """启动设备发现服务"""
        if self.running:
            logger.warning("Device discovery service is already running")
            return
        
        try:
            # 创建UDP socket
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.socket.bind(('', self.broadcast_port))
            self.socket.settimeout(1.0)  # 1秒超时
            
            self.running = True
            
            # 启动发现线程
            self.discovery_thread = threading.Thread(target=self._discovery_loop, daemon=True)
            self.discovery_thread.start()
            
            # 启动清理线程
            self.cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
            self.cleanup_thread.start()
            
            logger.info(f"Device discovery service started on port {self.broadcast_port}")
            
        except Exception as e:
            logger.error(f"Failed to start device discovery service: {e}")
            self.stop()
            raise
    
    def stop(self):
        """停止设备发现服务"""
        if not self.running:
            return
        
        self.running = False
        
        if self.socket:
            self.socket.close()
            self.socket = None
        
        if self.discovery_thread:
            self.discovery_thread.join(timeout=2)
        
        if self.cleanup_thread:
            self.cleanup_thread.join(timeout=2)
        
        logger.info("Device discovery service stopped")
    
    def _discovery_loop(self):
        """设备发现循环"""
        logger.info("Device discovery loop started")
        
        while self.running:
            try:
                # 接收广播数据
                data, addr = self.socket.recvfrom(1024)
                self._handle_broadcast_message(data, addr)
                
            except socket.timeout:
                # 超时是正常的，继续循环
                continue
            except Exception as e:
                if self.running:
                    logger.error(f"Error in discovery loop: {e}")
                break
        
        logger.info("Device discovery loop stopped")
    
    def _cleanup_loop(self):
        """清理过期设备循环"""
        while self.running:
            try:
                time.sleep(5)  # 每5秒检查一次
                self._cleanup_expired_devices()
            except Exception as e:
                if self.running:
                    logger.error(f"Error in cleanup loop: {e}")
        
        logger.info("Device cleanup loop stopped")
    
    def _handle_broadcast_message(self, data: bytes, addr: tuple):
        """处理广播消息"""
        try:
            # 解析JSON数据
            message = json.loads(data.decode('utf-8'))
            
            # 验证消息类型
            if message.get('type') != 'STUDENT_FOTO_DEVICE':
                return
            
            # 提取设备信息
            device_name = message.get('deviceName', 'Unknown Device')
            device_ip = message.get('deviceIp', addr[0])
            tcp_port = message.get('tcpPort', 8080)
            timestamp = message.get('timestamp', int(time.time() * 1000))
            
            # 更新或添加设备
            self._update_device(device_name, device_ip, tcp_port, timestamp)
            
            logger.debug(f"Received broadcast from {device_name} at {device_ip}:{tcp_port}")
            
        except json.JSONDecodeError:
            logger.warning(f"Invalid JSON in broadcast from {addr[0]}")
        except Exception as e:
            logger.error(f"Error handling broadcast message: {e}")
    
    def _update_device(self, device_name: str, device_ip: str, tcp_port: int, timestamp: int):
        """更新或添加设备"""
        if device_ip in self.devices:
            # 更新现有设备
            device = self.devices[device_ip]
            device.device_name = device_name
            device.tcp_port = tcp_port
            device.timestamp = timestamp
            device.update_last_seen()
            
            if self.on_device_updated:
                self.on_device_updated(device)
        else:
            # 添加新设备
            device = DeviceInfo(device_name, device_ip, tcp_port, timestamp)
            self.devices[device_ip] = device
            
            if self.on_device_found:
                self.on_device_found(device)
            
            logger.info(f"Found new device: {device}")
    
    def _cleanup_expired_devices(self):
        """清理过期设备"""
        expired_devices = []
        
        for device_ip, device in list(self.devices.items()):
            if device.is_expired():
                expired_devices.append(device)
                del self.devices[device_ip]
        
        for device in expired_devices:
            logger.info(f"Device expired: {device}")
            if self.on_device_lost:
                self.on_device_lost(device)
    
    def get_devices(self) -> List[DeviceInfo]:
        """获取所有发现的设备"""
        return list(self.devices.values())
    
    def get_device_by_ip(self, ip: str) -> Optional[DeviceInfo]:
        """根据IP获取设备"""
        return self.devices.get(ip)
    
    def clear_devices(self):
        """清空设备列表"""
        self.devices.clear()
    
    def is_running(self) -> bool:
        """检查服务是否运行中"""
        return self.running
