#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导出对话框
"""

import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                             QPushButton, QLineEdit, QProgressBar, QTextEdit,
                             QGroupBox, QFileDialog, QMessageBox)
from PyQt5.QtCore import Qt, pyqtSlot
from PyQt5.QtGui import QFont

from core.photo_exporter import PhotoExportThread
import config

class ExportDialog(QDialog):
    """导出对话框"""
    
    def __init__(self, exporter, parent=None):
        super().__init__(parent)
        self.exporter = exporter
        self.export_thread = None
        
        self.setWindowTitle("导出照片")
        self.setModal(True)
        self.resize(500, 400)
        
        self.init_ui()
        self.load_statistics()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 统计信息组
        stats_group = QGroupBox("导出统计")
        stats_layout = QVBoxLayout(stats_group)
        
        self.stats_label = QLabel("正在加载统计信息...")
        self.stats_label.setFont(QFont("Arial", 10))
        stats_layout.addWidget(self.stats_label)
        
        layout.addWidget(stats_group)
        
        # 导出设置组
        export_group = QGroupBox("导出设置")
        export_layout = QVBoxLayout(export_group)
        
        # 导出目录选择
        dir_layout = QHBoxLayout()
        dir_layout.addWidget(QLabel("导出目录:"))
        
        self.dir_edit = QLineEdit()
        self.dir_edit.setText(config.DEFAULT_EXPORT_DIR)
        dir_layout.addWidget(self.dir_edit)
        
        self.browse_btn = QPushButton("浏览...")
        self.browse_btn.clicked.connect(self.browse_directory)
        dir_layout.addWidget(self.browse_btn)
        
        export_layout.addLayout(dir_layout)
        
        layout.addWidget(export_group)
        
        # 进度显示组
        progress_group = QGroupBox("导出进度")
        progress_layout = QVBoxLayout(progress_group)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        progress_layout.addWidget(self.progress_bar)
        
        self.progress_label = QLabel("")
        self.progress_label.setVisible(False)
        progress_layout.addWidget(self.progress_label)
        
        layout.addWidget(progress_group)
        
        # 日志显示
        log_group = QGroupBox("导出日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        layout.addWidget(log_group)
        
        # 按钮组
        button_layout = QHBoxLayout()
        
        self.export_btn = QPushButton("开始导出")
        self.export_btn.clicked.connect(self.start_export)
        button_layout.addWidget(self.export_btn)
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        button_layout.addStretch()
        
        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.accept)
        self.close_btn.setEnabled(False)
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
    
    def load_statistics(self):
        """加载统计信息"""
        try:
            stats = self.exporter.get_export_statistics()
            
            stats_text = f"""
已完成任务: {stats['total_completed']} 个
有照片任务: {stats['with_photo']} 个
无照片任务: {stats['without_photo']} 个
涉及人数: {stats['unique_persons']} 人

注意：只有包含照片的已完成任务才会被导出
            """.strip()
            
            self.stats_label.setText(stats_text)
            
            # 如果没有可导出的照片，禁用导出按钮
            if stats['with_photo'] == 0:
                self.export_btn.setEnabled(False)
                self.export_btn.setText("无可导出照片")
                
        except Exception as e:
            self.stats_label.setText(f"加载统计信息失败: {e}")
            self.export_btn.setEnabled(False)
    
    def browse_directory(self):
        """浏览目录"""
        directory = QFileDialog.getExistingDirectory(
            self, "选择导出目录", self.dir_edit.text()
        )
        
        if directory:
            self.dir_edit.setText(directory)
    
    def start_export(self):
        """开始导出"""
        export_dir = self.dir_edit.text().strip()
        
        if not export_dir:
            QMessageBox.warning(self, "输入错误", "请选择导出目录")
            return
        
        # 检查目录是否存在，不存在则创建
        try:
            if not os.path.exists(export_dir):
                os.makedirs(export_dir)
        except Exception as e:
            QMessageBox.critical(self, "目录错误", f"无法创建导出目录：\n{str(e)}")
            return
        
        # 禁用控件
        self.export_btn.setEnabled(False)
        self.browse_btn.setEnabled(False)
        self.dir_edit.setEnabled(False)
        self.cancel_btn.setEnabled(False)
        
        # 显示进度控件
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.progress_label.setVisible(True)
        self.progress_label.setText("准备导出...")
        
        # 清空日志
        self.log_text.clear()
        self.add_log("开始导出照片...")
        
        # 创建并启动导出线程
        self.export_thread = PhotoExportThread(self.exporter, export_dir)
        self.export_thread.progress_updated.connect(self.on_progress_updated)
        self.export_thread.export_finished.connect(self.on_export_finished)
        self.export_thread.export_error.connect(self.on_export_error)
        self.export_thread.start()
    
    @pyqtSlot(int, int)
    def on_progress_updated(self, current, total):
        """更新进度"""
        self.progress_bar.setMaximum(total)
        self.progress_bar.setValue(current)
        self.progress_label.setText(f"正在导出: {current}/{total}")
    
    @pyqtSlot(dict)
    def on_export_finished(self, result):
        """导出完成"""
        self.progress_label.setText("导出完成")
        
        self.add_log(f"导出完成！")
        self.add_log(f"成功导出: {result['exported']} 张照片")
        self.add_log(f"导出失败: {result['failed']} 张照片")
        self.add_log(f"总计处理: {result['total']} 个任务")
        
        # 显示结果对话框
        QMessageBox.information(
            self, "导出完成",
            f"照片导出完成！\n\n"
            f"成功导出: {result['exported']} 张\n"
            f"导出失败: {result['failed']} 张\n"
            f"总计处理: {result['total']} 个任务\n\n"
            f"导出目录: {self.dir_edit.text()}"
        )
        
        # 恢复控件状态
        self.export_btn.setText("重新导出")
        self.export_btn.setEnabled(True)
        self.browse_btn.setEnabled(True)
        self.dir_edit.setEnabled(True)
        self.cancel_btn.setEnabled(True)
        self.close_btn.setEnabled(True)
    
    @pyqtSlot(str)
    def on_export_error(self, error_message):
        """导出错误"""
        self.progress_label.setText("导出失败")
        self.add_log(f"导出失败: {error_message}")
        
        QMessageBox.critical(self, "导出失败", f"导出过程中发生错误：\n{error_message}")
        
        # 恢复控件状态
        self.export_btn.setEnabled(True)
        self.browse_btn.setEnabled(True)
        self.dir_edit.setEnabled(True)
        self.cancel_btn.setEnabled(True)
        self.close_btn.setEnabled(True)
    
    def add_log(self, message):
        """添加日志"""
        self.log_text.append(message)
    
    def closeEvent(self, event):
        """关闭事件"""
        if self.export_thread and self.export_thread.isRunning():
            reply = QMessageBox.question(
                self, "确认关闭",
                "导出正在进行中，确定要关闭吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                self.export_thread.terminate()
                self.export_thread.wait()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()
