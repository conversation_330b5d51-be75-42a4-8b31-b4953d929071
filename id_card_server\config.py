#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
"""

# 应用配置
APP_NAME = "身份证读卡器服务端"
APP_VERSION = "1.0.0"

# 数据库配置
DATABASE_PATH = "photo_tasks.db"

# 网络配置
SERVER_PORT = 9090
SERVER_HOST = "0.0.0.0"

# 广播配置
BROADCAST_PORT = 9091
BROADCAST_INTERVAL = 3  # 广播间隔（秒）
BROADCAST_MESSAGE = "STUDENT_PHOTO_SERVER"

# 读卡器配置
CARD_READER_PORT = 1001  # USB端口

# 界面配置
WINDOW_WIDTH = 1000
WINDOW_HEIGHT = 700

# 导出配置
DEFAULT_EXPORT_DIR = "./exported_photos"
DEFAULT_CSV_FILE = "./task_list.csv"

# 配置文件路径
PHOTO_SETTINGS_FILE = "photo_settings.json"

# 日志配置
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# 默认照片参数设置
DEFAULT_PHOTO_SETTINGS = {
    # 基本设置
    "size": {
        "name": "一寸",
        "width": 295,
        "height": 413,
        "description": "25mm × 35mm"
    },
    "background_color": {
        "name": "蓝色",
        "hex_value": "#438EDB",
        "description": "标准蓝色背景"
    },
    "matting_model": {
        "name": "modnet_photographic_portrait_matting",
        "model_id": "modnet_photographic_portrait_matting",
        "description": "通用人像抠图模型"
    },
    "face_detect_model": {
        "name": "mtcnn",
        "model_id": "mtcnn",
        "description": "MTCNN人脸检测模型"
    },

    # 高级设置
    "dpi": 300,
    "hd": False,  # 默认不生成高清照片
    "face_alignment": True,
    "head_measure_ratio": 0.2,
    "head_height_ratio": 0.45,
    "top_distance_max": 0.12,
    "top_distance_min": 0.1,

    # 图像调整
    "brightness_strength": 0,
    "contrast_strength": 0,
    "sharpen_strength": 0,
    "saturation_strength": 0,

    # 输出设置
    "kb_size": None,
    "render_mode": 0  # 0=纯色，1=上下渐变，2=中心渐变
}

# 预定义照片尺寸
PHOTO_SIZES = [
    {"name": "一寸", "width": 295, "height": 413, "description": "25mm × 35mm"},
    {"name": "二寸", "width": 413, "height": 579, "description": "35mm × 49mm"},
    {"name": "小二寸", "width": 413, "height": 531, "description": "35mm × 45mm"},
    {"name": "大二寸", "width": 413, "height": 626, "description": "35mm × 53mm"},
    {"name": "三寸", "width": 649, "height": 991, "description": "55mm × 84mm"},
    {"name": "五寸", "width": 1051, "height": 1500, "description": "89mm × 127mm"},
    {"name": "护照", "width": 390, "height": 567, "description": "33mm × 48mm"},
    {"name": "驾驶证", "width": 260, "height": 378, "description": "22mm × 32mm"},
    {"name": "教师资格证", "width": 295, "height": 413, "description": "25mm × 35mm"},
]

# 预定义背景色
BACKGROUND_COLORS = [
    {"name": "蓝色", "hex_value": "#438EDB", "description": "标准蓝色背景"},
    {"name": "红色", "hex_value": "#FF0000", "description": "标准红色背景"},
    {"name": "白色", "hex_value": "#FFFFFF", "description": "标准白色背景"},
    {"name": "浅蓝色", "hex_value": "#87CEEB", "description": "浅蓝色背景"},
    {"name": "深蓝色", "hex_value": "#191970", "description": "深蓝色背景"},
]

# 预定义抠图模型
MATTING_MODELS = [
    {"name": "modnet_photographic_portrait_matting", "model_id": "modnet_photographic_portrait_matting", "description": "通用人像抠图模型"},
    {"name": "hivision_modnet", "model_id": "hivision_modnet", "description": "HivisionIDPhotos专用模型"},
    {"name": "rmbg-1.4", "model_id": "rmbg-1.4", "description": "RMBG高精度模型"},
]

# 预定义人脸检测模型
FACE_DETECT_MODELS = [
    {"name": "mtcnn", "model_id": "mtcnn", "description": "MTCNN人脸检测模型"},
    {"name": "face_plusplus", "model_id": "face_plusplus", "description": "Face++人脸检测模型"},
    {"name": "retinaface", "model_id": "retinaface", "description": "RetinaFace人脸检测模型"},
]
