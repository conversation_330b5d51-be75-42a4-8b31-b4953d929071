<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="请确认处理后的证件照："
        android:textSize="16sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <ImageView
        android:id="@+id/processed_photo_image"
        android:layout_width="300dp"
        android:layout_height="400dp"
        android:layout_gravity="center"
        android:scaleType="fitCenter"
        android:background="@android:color/white"
        android:layout_marginBottom="16dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center">

        <Button
            android:id="@+id/retake_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="重新拍照"
            android:backgroundTint="@android:color/holo_orange_dark"
            android:textColor="@android:color/white"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/confirm_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="确认上传"
            android:backgroundTint="@android:color/holo_green_dark"
            android:textColor="@android:color/white"
            android:layout_marginStart="8dp" />

    </LinearLayout>

</LinearLayout>
