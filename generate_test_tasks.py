#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成测试任务脚本
"""

import sys
import os
sys.path.append('.')

from id_card_server.core.database import DatabaseManager

def generate_test_tasks():
    """生成测试任务"""
    try:
        db = DatabaseManager()
        
        print('=== 生成测试任务 ===')
        
        # 生成5个测试任务
        count = 5
        success = db.generate_test_tasks(count)
        
        if success:
            print(f'✅ 成功生成 {count} 个测试任务')
            
            # 检查生成结果
            print('\n=== 检查生成结果 ===')
            test_stats = db.get_task_statistics("test_tasks")
            print(f'测试任务总数: {test_stats["total"]}')
            print(f'测试待分配: {test_stats["pending"]}')
            
            # 显示测试任务列表
            test_unassigned = db.get_unassigned_tasks("test_tasks")
            print(f'\n=== 测试任务列表 ===')
            print(f'未分配测试任务数量: {len(test_unassigned)}')
            for i, task in enumerate(test_unassigned):
                print(f'{i+1}. 任务ID: {task["task_id"]}, 姓名: {task["name"]}, 身份证: {task["id_number"]}')
                
        else:
            print('❌ 生成测试任务失败')
            
    except Exception as e:
        print(f'❌ 生成测试任务异常: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    generate_test_tasks()
