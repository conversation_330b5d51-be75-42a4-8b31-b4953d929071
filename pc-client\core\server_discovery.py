#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
服务端发现功能
通过UDP广播发现网络中的服务端
"""

import socket
import json
import threading
import time
import logging
from typing import Dict, List, Callable
from PyQt5.QtCore import QObject, pyqtSignal
import config

logger = logging.getLogger(__name__)

class ServerInfo:
    """服务端信息"""
    def __init__(self, data: Dict):
        self.server_ip = data.get("server_ip", "")
        self.server_port = data.get("server_port", 9090)
        self.server_name = data.get("server_name", "未知服务端")
        self.version = data.get("version", "")
        self.timestamp = data.get("timestamp", 0)
        self.last_seen = time.time()
    
    def update(self, data: Dict):
        """更新服务端信息"""
        self.server_port = data.get("server_port", self.server_port)
        self.server_name = data.get("server_name", self.server_name)
        self.version = data.get("version", self.version)
        self.timestamp = data.get("timestamp", self.timestamp)
        self.last_seen = time.time()
    
    def is_expired(self, timeout: int = 15) -> bool:
        """检查服务端信息是否过期"""
        return time.time() - self.last_seen > timeout
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            "server_ip": self.server_ip,
            "server_port": self.server_port,
            "server_name": self.server_name,
            "version": self.version,
            "last_seen": self.last_seen
        }

class ServerDiscovery(QObject):
    """服务端发现类"""
    
    # 信号定义
    server_found = pyqtSignal(dict)      # 发现服务端
    server_lost = pyqtSignal(str)        # 服务端丢失
    discovery_started = pyqtSignal()     # 发现开始
    discovery_stopped = pyqtSignal()     # 发现停止
    error_occurred = pyqtSignal(str)     # 错误发生
    
    def __init__(self, broadcast_port=9091):
        super().__init__()
        self.broadcast_port = broadcast_port
        self.running = False
        self.discovery_thread = None
        self.cleanup_thread = None
        self.socket = None
        self.servers = {}  # 存储发现的服务端信息
        
    def start_discovery(self):
        """开始发现服务端"""
        if self.running:
            return
            
        try:
            self.running = True
            
            # 启动发现线程
            self.discovery_thread = threading.Thread(target=self._discovery_loop, daemon=True)
            self.discovery_thread.start()
            
            # 启动清理线程
            self.cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
            self.cleanup_thread.start()
            
            self.discovery_started.emit()
            logger.info(f"服务端发现已启动，监听端口: {self.broadcast_port}")
            
        except Exception as e:
            self.running = False
            error_msg = f"启动服务端发现失败: {e}"
            self.error_occurred.emit(error_msg)
            logger.error(error_msg)
    
    def stop_discovery(self):
        """停止发现服务端"""
        if not self.running:
            return
            
        self.running = False
        
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None
            
        if self.discovery_thread and self.discovery_thread.is_alive():
            self.discovery_thread.join(timeout=2)
            
        if self.cleanup_thread and self.cleanup_thread.is_alive():
            self.cleanup_thread.join(timeout=2)
            
        self.servers.clear()
        self.discovery_stopped.emit()
        logger.info("服务端发现已停止")
    
    def _discovery_loop(self):
        """发现循环"""
        try:
            # 创建UDP套接字
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.socket.bind(('', self.broadcast_port))
            self.socket.settimeout(1.0)  # 设置超时，便于检查running状态
            
            while self.running:
                try:
                    # 接收广播消息
                    data, addr = self.socket.recvfrom(1024)
                    message = json.loads(data.decode('utf-8'))
                    
                    # 检查是否是服务端广播
                    if (message.get("type") == "server_discovery" and 
                        message.get("message") == "STUDENT_PHOTO_SERVER"):
                        
                        server_ip = message.get("server_ip", addr[0])
                        
                        # 更新或添加服务端信息
                        if server_ip in self.servers:
                            self.servers[server_ip].update(message)
                        else:
                            server_info = ServerInfo(message)
                            self.servers[server_ip] = server_info
                            
                            # 发出发现信号
                            self.server_found.emit(server_info.to_dict())
                            logger.info(f"发现服务端: {server_ip}:{server_info.server_port}")
                            
                except socket.timeout:
                    # 超时是正常的，继续循环
                    continue
                except json.JSONDecodeError:
                    # 忽略无效的JSON消息
                    continue
                except Exception as e:
                    if self.running:
                        logger.error(f"接收广播消息失败: {e}")
                        
        except Exception as e:
            if self.running:
                error_msg = f"服务端发现异常: {e}"
                self.error_occurred.emit(error_msg)
                logger.error(error_msg)
        finally:
            if self.socket:
                try:
                    self.socket.close()
                except:
                    pass
                self.socket = None
    
    def _cleanup_loop(self):
        """清理过期服务端的循环"""
        while self.running:
            try:
                # 检查过期的服务端
                expired_servers = []
                for server_ip, server_info in self.servers.items():
                    if server_info.is_expired():
                        expired_servers.append(server_ip)
                
                # 移除过期的服务端
                for server_ip in expired_servers:
                    del self.servers[server_ip]
                    self.server_lost.emit(server_ip)
                    logger.info(f"服务端已离线: {server_ip}")
                
                # 等待下次检查
                time.sleep(5)
                
            except Exception as e:
                if self.running:
                    logger.error(f"清理过期服务端异常: {e}")
                time.sleep(5)
    
    def get_servers(self) -> List[Dict]:
        """获取当前发现的服务端列表"""
        return [server.to_dict() for server in self.servers.values()]
    
    def get_server_count(self) -> int:
        """获取发现的服务端数量"""
        return len(self.servers)
    
    def is_running(self) -> bool:
        """检查是否正在运行"""
        return self.running
